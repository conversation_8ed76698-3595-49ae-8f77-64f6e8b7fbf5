package com.czur.starry.device.hdmiin

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.camera2.CameraManager
import android.media.AudioManager
import android.media.AudioManager.ADJUST_LOWER
import android.media.AudioManager.ADJUST_RAISE
import android.media.AudioManager.FLAG_SHOW_UI
import android.media.AudioManager.STREAM_MUSIC
import android.os.Bundle
import android.view.KeyEvent
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.BaseActivity
import com.czur.starry.device.baselib.common.ACTION_BROADCAST_SOURCE_CHANGE
import com.czur.starry.device.baselib.common.KEY_SOURCE_CHANGE
import com.czur.starry.device.baselib.common.KEY_STARTUP_COMPLETE
import com.czur.starry.device.baselib.common.VALUE_SOURCE_CHANGE_HDMI
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.hdmilib.CameraView
import com.czur.starry.device.hdmilib.HDMIIFStatus
import com.czur.starry.device.hdmilib.HDMIUtil
import com.czur.uilib.choose.CZImageCheckBox
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay

class MainActivity : BaseActivity() {
    override fun getLayout(): Int = R.layout.activity_main

    companion object {
        private const val TAG = "HDMI-MainActivity"
    }

    override val banBackGround: Boolean = true
    override val careScreenLock: Boolean = true

    private val viewFinder: CameraView by findView(R.id.viewFinder)
    private val hdmiAutoOpenCb: CZImageCheckBox by findView(R.id.hdmiAutoOpenCb)
    private val uiSearch: ConstraintLayout by findView(R.id.uiSearch)
    private val uiNoSign: ConstraintLayout by findView(R.id.uiNoSign)
    private val hdmiSettingGroup: LinearLayout by findView(R.id.hdmiSettingGroup)
    private val backBtn: CommonButton by findView(R.id.backBtn)

    private val audioUtil = AudioUtil()
    private val systemManager = SystemManagerProxy()
    private lateinit var cameraManager: CameraManager

    private val hdmiUtil by lazy {
        HDMIUtil(cameraManager, this, viewFinder)
    }

    private val audioManager: AudioManager by lazy {
        getSystemService(AudioManager::class.java)
    }

    override fun skipAndFinish(): Boolean {
        // 初期设定没有完成, 则不启动HDMI
        return !getBooleanSystemProp(KEY_STARTUP_COMPLETE, false)
    }

    override fun initListener() {
        super.initListener()

        val filter = IntentFilter().apply {
            addAction(ESHARE_EXIT_ACTION)
            addAction(HDMI_EXIT_ACTION)
        }
        this.registerReceiver(souceChannelReciver, filter)
    }

    override fun initViews() {
        super.initViews()
        viewFinder.gone()
        uiSearch.gone()
        uiNoSign.gone()
        hdmiSettingGroup.gone()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        cameraManager = getSystemService(AppCompatActivity.CAMERA_SERVICE) as CameraManager
        // 关闭正在进行中的投屏
        EShareServerSDK.getSingleton(this).stopAllCast()
        hdmiAutoOpenCb.isChecked = HDMIUtil.getAutoOpen()


        hdmiUtil.onHdmiChangeListener = {
            if (it == HDMIIFStatus.OUT) {
                logTagD(TAG, "HDMI 线材拔出, 退出")
                finish()
            }
        }

        hdmiUtil.hdmiStatusLive.observe(this) { status ->
            logTagD(TAG, "status:${status}")
            // 修改保持屏幕常量的方法
            changeKeepScreenOn(status == HDMIUtil.HDMIStatus.PREVIEW)
            when (status!!) {
                HDMIUtil.HDMIStatus.PREPARE -> {
                    // 准备阶段
                    uiSearch.show()
                    uiNoSign.gone()
                    hdmiSettingGroup.show()
                    viewFinder.show()
                }

                HDMIUtil.HDMIStatus.PREVIEW -> {
                    // 预览阶段
                    uiSearch.gone()
                    uiNoSign.gone()
                    hdmiSettingGroup.gone()
                }

                HDMIUtil.HDMIStatus.RESET -> {
                    hdmiUtil.rePreview()
                }

                HDMIUtil.HDMIStatus.IDLE -> {
                    // 无信号
                    launch {
                        delay(100)
                        if (!isFinishing && hdmiUtil.hdmiIFStatus == HDMIIFStatus.IN) {
                            uiSearch.gone()
                            uiNoSign.show()
                            hdmiSettingGroup.show()
                        }
                    }
                }
            }
        }

        viewFinder.onReadyCallback {
            // 启动HDMI功能
            hdmiUtil.start()
        }

        // 返回按钮
        backBtn.setOnClickListener {
            finish()
        }

        // HDMI 自动打开
        hdmiAutoOpenCb.onCheckChangeListener = { checked ->
            HDMIUtil.enableAutoOpen(checked)
        }
    }

    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP -> {
                adjustTheVolume(ADJUST_RAISE)
                true
            }

            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                adjustTheVolume(ADJUST_LOWER)
                true
            }

            else -> super.onInterceptKeyDown(keyCode, event)
        }
    }

    private fun adjustTheVolume(direction: Int) {
        audioManager.adjustStreamVolume(STREAM_MUSIC, direction, FLAG_SHOW_UI)
    }

    override fun onResume() {
        super.onResume()
        if (systemManager.needPerfConstraint(SystemManagerProxy.PerfConstraintScene.HDMI)) {
            logTagW(TAG, "HDMI 需要性能约束")
            toast(R.string.toast_pref_constraint)
        } else {
            logTagV(TAG, "HDMI 不需要性能约束")
        }
        sendChangSourceBroadcast()
    }


    private fun sendChangSourceBroadcast() {
        logTagV(TAG, "源切换广播：HDMI")
        val intent = Intent(ACTION_BROADCAST_SOURCE_CHANGE).apply {
            putExtra(KEY_SOURCE_CHANGE, VALUE_SOURCE_CHANGE_HDMI)
        }
        sendBroadcast(intent)
    }

    override fun onScreenOFF() {
        super.onScreenOFF()
        finish()
    }


    override fun finish() {
        logTagD(TAG, "HDMI finish")
        super.finish()
    }

    override fun onStop() {
        super.onStop()
        if (!isFinishing) {
            finish()
            lifecycleScope.cancel()
        }
    }

    override fun onDestroy() {
        logTagD(TAG, "HDMI 页面销毁")
        super.onDestroy()
        this.unregisterReceiver(souceChannelReciver)
    }

    //souce键切换StarryOS广播
    private val souceChannelReciver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            finish()
        }
    }
}