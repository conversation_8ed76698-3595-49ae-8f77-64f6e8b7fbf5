<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <com.czur.starry.device.hdmilib.CameraView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cameraDisplayMode="texture" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/uiSearch"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg_color">

        <ProgressBar
            android:id="@+id/uiSearchProgress"
            android:layout_width="100px"
            android:layout_height="100px"
            android:layout_marginBottom="50px"
            android:indeterminateTint="@color/white"
            app:layout_constraintBottom_toTopOf="@id/uiSearchText"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/uiSearchText"
            style="@style/style_hdmi_info_tv"
            android:text="@string/info_hdmi_searching"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/uiNoSign"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg_color"
        android:visibility="visible">

        <ImageView
            android:id="@+id/noSignLogoIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="50px"
            android:src="@drawable/ic_hdmi_no_sign"
            app:layout_constraintBottom_toTopOf="@id/noSignTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/noSignTv"
            style="@style/style_hdmi_info_tv"
            android:text="@string/info_hdmi_no_sign"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/backBtn"
            android:layout_width="300px"
            android:layout_height="80px"
            android:layout_marginBottom="60px"
            android:text="@string/str_back"
            android:textSize="30px"
            android:textStyle="bold"
            app:baselib_theme="white2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/hdmiSettingGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="180px"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <com.czur.uilib.choose.CZImageCheckBox
            android:id="@+id/hdmiAutoOpenCb"
            android:layout_width="50px"
            android:layout_height="50px"
            app:checkedImg="@drawable/ic_check"
            app:unCheckedImg="@drawable/ic_uncheck" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20px"
            android:text="@string/str_hdmi_auto_open"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold" />
    </LinearLayout>
</FrameLayout>