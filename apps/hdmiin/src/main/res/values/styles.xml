<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="style_hdmi_info_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">48px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginBottom">448px</item>
    </style>

    <style name="MyTranslucentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
</resources>