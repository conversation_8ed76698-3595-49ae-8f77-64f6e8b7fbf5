package com.czur.starry.device.launcher

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.czur.starry.device.launcher.app.App
import com.czur.starry.device.launcher.guide.ExtendTipsDao
import com.czur.starry.device.launcher.guide.ExtendTipsEntity
import com.czur.starry.device.launcher.guide.GuideInfoDao
import com.czur.starry.device.launcher.guide.GuideInfoEntity

/**
 * Created by 陈丰尧 on 2022/5/19
 */
@Database(
    entities = [GuideInfoEntity::class, ExtendTipsEntity::class],
    version = 2,
    exportSchema = false
)
abstract class LauncherDataBase : RoomDatabase() {
    abstract fun guideDao(): GuideInfoDao
    abstract fun extendTipsDao(): ExtendTipsDao

    companion object {
        val instance = Single.sin
    }

    private object Single {
        val sin: LauncherDataBase = Room.databaseBuilder(
            App.context,
            LauncherDataBase::class.java,
            "Launcher.db"
        ).addMigrations(MIGRATION_1_2)
            .build()
    }

}

private val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("CREATE TABLE IF NOT EXISTS `tab_extend_tips_info` (`tipsKey` TEXT NOT NULL, `pkgName` TEXT NOT NULL, `showTipsCount` INTEGER NOT NULL, `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)")
    }

}