package com.czur.starry.device.launcher.backdrop

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.czur.czurutils.log.logTagV

/**
 * Created by 陈丰尧 on 2024/7/24
 */
private const val TAG = "BackdropChangeReceiver"
class BackdropChangeReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        logTagV(TAG, "壁纸更换了")
        BackdropManager.onBackdropChange()
    }
}