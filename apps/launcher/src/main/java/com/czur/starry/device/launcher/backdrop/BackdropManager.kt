package com.czur.starry.device.launcher.backdrop

import android.app.WallpaperManager
import android.graphics.Bitmap
import android.graphics.Color
import androidx.core.graphics.drawable.toBitmap
import androidx.core.graphics.get
import androidx.palette.graphics.Palette
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.baselib.utils.screenX
import com.czur.starry.device.launcher.app.App
import com.czur.starry.device.launcher.widget.BlurImageView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2024/7/24
 * 用来管理桌面壁纸的, 主要是用来监听壁纸的变化, 以便及时处理模糊图片
 */
object BackdropManager {
    private const val TAG = "BackdropManager"
    private const val BLUR_IMG_NAME = "blur_img.png"

    private val scope = MainScope()
    private val wpm: WallpaperManager by lazy {
        WallpaperManager.getInstance(App.context)
    }

    private var blurBackdropBitmap: Bitmap? = null
    private val _eventFlow = MutableSharedFlow<BackdropEvent>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    // 事件Flow
    val eventFlow = _eventFlow.asSharedFlow()

    private val isPreparing: AtomicBoolean = AtomicBoolean(false)

    private val _backdropColorFlow = MutableStateFlow(BackdropColor.Dark)

    // 公开的背景颜色Flow
    val backdropColorFlow = _backdropColorFlow.asStateFlow()
    val backdropColor: BackdropColor
        get() = _backdropColorFlow.value


    enum class BackdropEvent {
        CHANGE
    }

    enum class BackdropColor {
        Dark,
        Light,
    }

    /**
     * 使用Android Palette API分析Bitmap的颜色，判断是暗色还是亮色
     * @param bitmap 要分析的位图
     * @return BackdropColor.Dark 或 BackdropColor.Light
     */
    private fun analyzeBitmapColor(bitmap: Bitmap): BackdropColor {
        // 使用Android的Palette API来分析颜色
        val palette = Palette.from(bitmap)
            .maximumColorCount(16) // 限制颜色数量以提高性能
            .generate()

        // 获取主要颜色，优先级：主导色 > 鲜艳色 > 柔和色
        val dominantColor = palette.dominantSwatch
        val vibrantColor = palette.vibrantSwatch
        val mutedColor = palette.mutedSwatch

        // 选择最合适的颜色样本
        val selectedSwatch = dominantColor ?: vibrantColor ?: mutedColor

        val backdropColor = if (selectedSwatch != null) {
            // 直接分析主导色的亮度
            val swatchColor = selectedSwatch.rgb
            val red = Color.red(swatchColor)
            val green = Color.green(swatchColor)
            val blue = Color.blue(swatchColor)

            // 使用ITU-R BT.709标准的亮度计算公式
            val luminance = 0.2126 * red + 0.7152 * green + 0.0722 * blue
            val threshold = 128.0

            logTagD(TAG, "主导色分析: RGB($red,$green,$blue), 亮度=$luminance")

            if (luminance > threshold) {
                BackdropColor.Light
            } else {
                BackdropColor.Dark
            }
        } else {
            // 如果无法提取颜色，使用备用方案：分析整体亮度
            logTagW(TAG, "Palette无法提取颜色，使用备用亮度分析")
            analyzeBitmapLuminance(bitmap)
        }

        logTagD(
            TAG,
            "背景颜色分析结果: $backdropColor, 选中样本颜色: ${
                selectedSwatch?.rgb?.let {
                    String.format(
                        "#%06X",
                        0xFFFFFF and it
                    )
                }
            }"
        )

        return backdropColor
    }

    /**
     * 备用方案：直接分析bitmap的亮度
     */
    private fun analyzeBitmapLuminance(bitmap: Bitmap): BackdropColor {
        val width = bitmap.width
        val height = bitmap.height

        // 采样分析，提高性能
        val sampleSize = 10
        var totalLuminance = 0.0
        var sampleCount = 0

        for (x in 0 until width step sampleSize) {
            for (y in 0 until height step sampleSize) {
                val pixel = bitmap[x, y]
                val red = Color.red(pixel)
                val green = Color.green(pixel)
                val blue = Color.blue(pixel)

                // 使用ITU-R BT.709标准的亮度计算公式
                val luminance = 0.2126 * red + 0.7152 * green + 0.0722 * blue
                totalLuminance += luminance
                sampleCount++
            }
        }

        val averageLuminance = if (sampleCount > 0) totalLuminance / sampleCount else 0.0
        val threshold = 128.0

        return if (averageLuminance > threshold) {
            BackdropColor.Light
        } else {
            BackdropColor.Dark
        }
    }

    /**
     * 当桌面背景发生变化时调用
     */
    fun onBackdropChange() {
        scope.launch {
            withContext(Dispatchers.IO) {
                prepareBackdropManager()
            }
        }
    }

    suspend fun prepareBackdropManager() = withContext(Dispatchers.IO) {
        try {
            isPreparing.set(true)
            val lastBlurBitmap = blurBackdropBitmap
            launch {
                delay(10000)  // 等待10秒, 确保壁纸加载完成
                lastBlurBitmap?.recycle()
            }
            blurBackdropBitmap = null
            val bmp = wpm.drawable?.toBitmap(getScreenWidth(), getScreenHeight())
                ?: return@withContext    // 这个bmp不能recycler哦

            // 分析背景颜色
            val backdropColor = analyzeBitmapColor(bmp)
            _backdropColorFlow.value = backdropColor
            logTagD(TAG, "背景颜色分析结果: $backdropColor")

            // 保存到文件
            val blurBmp = bmp.blur(40)
            launch {
                val file = File(App.context.cacheDir, BLUR_IMG_NAME)
                blurBmp.saveToFile(file)
            }
            blurBackdropBitmap = blurBmp
            _eventFlow.emit(BackdropEvent.CHANGE)
        } finally {
            isPreparing.set(false)
        }

    }


    private fun getBmp(): Bitmap? {
        return blurBackdropBitmap
    }

    private suspend fun checkPrepare() {
        while (isPreparing.get()) {
            logTagW(TAG, "正在准备模糊图片, 请稍后")
            delay(150)
        }
        if (blurBackdropBitmap == null) {
            logTagW(TAG, "currentRegionDecoder为空")
            prepareBackdropManager()
        }
    }

    /**
     * 获取模糊图片
     */
    suspend fun getBlurBitmap(isRetry: Boolean = false): Bitmap? {
        checkPrepare()
        val bitmap = getBmp()
        if (bitmap == null) {
            logTagW(TAG, "生成模糊图片失败, isRetry:${isRetry}")
            if (!isRetry) {
                delay(500)  // 通常失败的原因是View没有加载完成, 获取不到宽高
                logTagV(TAG, "重试1次")
                getBlurBitmap( true)
            }
        }
        return bitmap
    }

    /**
     * 设置模糊图片到BlurImageView
     */
    suspend fun setBlurBitmapToBlurIv(blurView: BlurImageView) {
        val bitmap = getBlurBitmap()
        blurView.bitmap = bitmap
    }
}