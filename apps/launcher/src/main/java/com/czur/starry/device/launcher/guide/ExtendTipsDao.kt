package com.czur.starry.device.launcher.guide

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

/**
 * Created by 陈丰尧 on 2023/7/6
 */
@Dao
abstract class ExtendTipsDao {

    @Query("select * from tab_extend_tips_info where tipsKey = :tipsKey")
    abstract fun getExtendTipsEntities(tipsKey: String): List<ExtendTipsEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract fun updateExtendTipsCount(extendTipsEntity: ExtendTipsEntity)

    @Query("delete from tab_extend_tips_info where pkgName = :pkgName")
    abstract fun deleteByPkgName(pkgName: String)
}