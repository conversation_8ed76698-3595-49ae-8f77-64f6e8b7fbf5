package com.czur.starry.device.launcher.guide

import android.content.Context
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.KEY_TOUCH_BOARD_VERSION
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.baselib.utils.prop.getIntSystemProp
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.launcher.LauncherDataBase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/5/19
 */
object GuideUtil {
    private const val TAG = "GuideUtil"
    private const val MAX_TOTAL_COUNT = 100
    private const val MAX_TODAY_COUNT = 3

    private const val TODAY_PATTERN = "yyyyMMdd"

    private val appUtil: AppUtil by lazy(LazyThreadSafetyMode.NONE) {
        AppUtil()
    }

    /**
     * 扩展提示配置
     */
    suspend fun resetExtendTipsCount(pkgName: String) {
        if (pkgName !in extendTipsConfigMap) {
            logTagI(TAG, "pkgName:$pkgName 不在扩展提示列表中, 不需要重置")
            return
        }
        val extendTipsDao = LauncherDataBase.instance.extendTipsDao()
        withContext(Dispatchers.IO) {
            logTagD(TAG, "重置扩展提示次数:$pkgName")
            extendTipsDao.deleteByPkgName(pkgName)
        }
    }

    /**
     * 展示扩展提示
     */
    suspend fun showExtendTips(
        context: Context,
        pkgName: String,
    ) {
        val config = extendTipsConfigMap[pkgName] ?: return
        // 展示提示的条件
        if (!config.showTipCondition()) {
            logTagV(TAG, "展示扩展提示:${config.pkgName}, 不满足展示条件,忽略")
            return
        }

        val extendTipsDao = LauncherDataBase.instance.extendTipsDao()
        val extendEntities = withContext(Dispatchers.IO) {
            extendTipsDao.getExtendTipsEntities(config.tipsKey)
        }
        val sumCount = extendEntities.sumOf { it.showTipsCount }
        if (sumCount < config.showTipsCount) {
            logTagD(
                TAG,
                "展示扩展提示:${config.pkgName} - ${config.tipsKey}, 已经提示了:${sumCount}次"
            )

            // 组装Toast
            val toastStr = if (config.tipsStrConfig.needAppName) {
                // 需要显示App名称
                val appName = appUtil.getApplicationName(pkgName)?.let { it
                } ?: ""
                getString(config.tipsStrRes, appName)
            } else {
                getString(config.tipsStrRes)
            }

            context.toast(toastStr)
            // 更新数据库
            val extendEntity = extendEntities.find { it.pkgName == pkgName } ?: ExtendTipsEntity(
                config.tipsKey,
                pkgName,
                0
            )
            val insertEntity = extendEntity.copy(showTipsCount = extendEntity.showTipsCount + 1)
            withContext(Dispatchers.IO) {
                extendTipsDao.updateExtendTipsCount(insertEntity)
            }
        } else {
            logTagV(
                TAG,
                "展示扩展提示:${config.pkgName}, 已经提示了:${sumCount}次,忽略"
            )
        }
    }


    suspend fun startGuideWindow(context: Context, pkgName: String) {
        // 只有连接一代触控板时,才会显示提示
        val touchPadVersion = getIntSystemProp(KEY_TOUCH_BOARD_VERSION, 0)
        if (touchPadVersion != 1) {
            logTagI(TAG, "没有连接一代触控板(当前${touchPadVersion}):,不展示引导")
            return
        }

        val guideDao = LauncherDataBase.instance.guideDao()
        val totalCount = withContext(Dispatchers.IO) {
            guideDao.getTotalOpenTimes()
        }
        logTagV(TAG, "总共引导次数:${totalCount}")
        if (totalCount >= MAX_TOTAL_COUNT) {
            logTagI(TAG, "总次数超过:${MAX_TOTAL_COUNT},忽略")
            return
        }

        val todayStr = getTimeStr(TODAY_PATTERN, System.currentTimeMillis())

        val todayCount = withContext(Dispatchers.IO) {
            guideDao.getTodayShowGuideCount(pkgName, todayStr)
        }
        logTagV(TAG, "今日引导册数:${totalCount},${pkgName}")
        if (todayCount >= MAX_TODAY_COUNT) {
            logTagI(TAG, "今日次数超过:${MAX_TODAY_COUNT},忽略")
            return
        }
        ThirdPartGuideService.startThirdPartGuide(context)

        // 记录一次次数
        withContext(Dispatchers.IO) {
            guideDao.insertOpenData(GuideInfoEntity(pkgName, todayStr))
        }
    }
}
