package com.czur.starry.device.launcher.meeting

import android.content.Context
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.ONE_DAY

import com.czur.starry.device.launcher.data.bean.OtherQuickBootAppItem
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2022/8/23
 */
class NetAppDoubleCache(private val context: Context, private val appTag: String) {
    companion object {
        private const val TAG = "NetAppDoubleCache"
        private const val CACHE_TIME = ONE_DAY  // 缓存的时间是1分钟
    }

    private val cacheFile: File by lazy {

        File(context.cacheDir.also {
            if (!it.exists()) {
                it.mkdirs()
            }
        }, "netApp-${appTag}.json")
    }


    private val gson = Gson()
    private val gsonToken = object :
        TypeToken<List<OtherQuickBootAppItem>>() {}.type

    private var lastCacheTime: Long = -1L
    private var lastCacheData: List<OtherQuickBootAppItem>? = null


    suspend fun loadFromCache(): List<OtherQuickBootAppItem>? {
        return withContext(Dispatchers.IO) {
            // 检查内存缓存
            lastCacheData?.let {
                if (System.currentTimeMillis() - lastCacheTime < CACHE_TIME) {
                    // 直接使用内存缓存
                    return@withContext it
                }
            }
            // 内存缓存没找到, 比如重启
            if (!cacheFile.exists() || System.currentTimeMillis() - cacheFile.lastModified() >= CACHE_TIME) {
                logTagV(TAG, "网络app缓存过期/缓存不存在")
                null
            } else {
                try {
                    cacheFile.reader().use {
                        gson.fromJson<List<OtherQuickBootAppItem>>(it, gsonToken)
                    }.also {
                        // 重新写入内存缓存
                        lastCacheData = it
                        lastCacheTime = cacheFile.lastModified()
                    }
                } catch (tr: Throwable) {
                    logTagE(TAG, "读取网络app缓存失败", tr = tr)
                    cacheFile.delete()
                    null
                }

            }
        }
    }

    /**
     * 强制读取缓存信息, 刚开机还没网的时候, 缓存可能过期, 此时就强制先用之前的数据显示出来, 之后再刷新
     */
    suspend fun loadCacheDataFocus(): List<OtherQuickBootAppItem>? {
        if (lastCacheData != null) return lastCacheData
        return withContext(Dispatchers.IO) {
            if (cacheFile.exists()) cacheFile.reader().use {
                gson.fromJson<List<OtherQuickBootAppItem>>(it, gsonToken)
            } else null
        }
    }

    suspend fun saveCache(data: List<OtherQuickBootAppItem>) {
        withContext(Dispatchers.IO) {
            // 先写入硬盘, 保证内存缓存后过期
            val json = gson.toJson(data)
            if (cacheFile.exists()) {
                cacheFile.delete()
            }
            cacheFile.writeText(json)

            lastCacheData = data
            lastCacheTime = System.currentTimeMillis()
        }
    }

    /**
     * 是否有缓存
     * 这里只判断内存缓存
     */
    fun hasCache(): Boolean {
        return lastCacheData != null && System.currentTimeMillis() - lastCacheTime < CACHE_TIME
    }
}