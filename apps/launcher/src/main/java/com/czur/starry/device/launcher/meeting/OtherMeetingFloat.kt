package com.czur.starry.device.launcher.meeting

import android.graphics.Bitmap
import android.view.View
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.LiveData
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.addItemDecoration
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.view.floating.KeyBackFloatFragment
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.NetMeetingApp
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.utils.bootApp

/**
 * Created by 陈丰尧 on 2022/8/23
 */
class OtherMeetingFloat(
    bgImg: Bitmap?,
    private val title: String,
    private val otherAppsLive: LiveData<List<NetMeetingApp>>
) : KeyBackFloatFragment(bgImg = bgImg) {
    override fun getLayoutId(): Int = R.layout.float_other_meeting

    companion object {
        private const val TAG = "OtherMeetingFloat"
    }

    private val otherMeetingAdapter = OtherMeetingAdapter()

    // 应用信息的ViewModel
    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })

    private val otherAppFloatTitleTv: TextView by findView(R.id.otherAppFloatTitleTv)
    private val closeBtn: View by findView(R.id.closeBtn)
    private val netAppRv: RecyclerView by findView(R.id.netAppRv)

    override fun initView() {
        super.initView()
        // 标题
        otherAppFloatTitleTv.text = title

        // 关闭对话框
        closeBtn.setOnDebounceClickListener {
            dismiss()
        }

        netAppRv.apply {
            layoutManager = GridLayoutManager(requireContext(), 6)   // 一行6个
            closeDefChangeAnimations()  // 关闭默认刷新动画
            adapter = otherMeetingAdapter   // 设置Adapter
            doOnItemClick { vh, _ ->
                bootOrInstallApp(vh.bindingAdapterPosition)
                true
            }
            addItemDecoration(RecyclerView.VERTICAL, 50)
        }
    }

    /**
     * 启动对应的会议App
     */
    private fun bootOrInstallApp(index: Int) {
        val clickApp = otherMeetingAdapter.getData(index)
        when (clickApp.status) {
            NetMeetingAppStatus.INSTALL -> {
                logTagV(TAG, "启动App:${clickApp.appName}")
                bootApp(clickApp.pkgName)
            }

            NetMeetingAppStatus.UNINSTALL -> {
                if (clickApp.downloadProcess >= 0) {
                    logTagD(TAG, "正在下载, 不做任何操作")
                } else {
                    logTagD(TAG, "安装App:${clickApp.appName}")
                    appsVM.downloadNetMeetingApp(clickApp)
                }
            }
        }
    }

    override fun initData() {
        super.initData()
        otherAppsLive.observe(this) {
            otherMeetingAdapter.setData(it)
        }
    }
}