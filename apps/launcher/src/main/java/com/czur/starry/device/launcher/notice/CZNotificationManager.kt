package com.czur.starry.device.launcher.notice

import android.graphics.BitmapFactory
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.cznotification.CZNotificationIntentWrapper
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import java.io.ObjectInputStream

/**
 * Created by 陈丰尧 on 2024/8/13
 */
object CZNotificationManager {
    private const val TAG = "CZNotificationManager"

    private val _showingListFlow = MutableStateFlow(listOf<CZNotificationEntity>())
    val showingFlow = _showingListFlow.map {
        it.firstOrNull()
    }
    private var idOffset = 0

    fun addOneCZNotification(
        belongPkgName: String,
        icon: ByteArray,                // 图标
        title: String,
        intentBlob: ByteArray,       // 点击通知的跳转
        removeAfterExecution: Boolean  // 执行后移除
    ): Int {
        val iconBmp = BitmapFactory.decodeByteArray(icon, 0, icon.size)

        val objInputStream = ObjectInputStream(intentBlob.inputStream())

        val intentWrapper = objInputStream.use {
            it.readObject() as CZNotificationIntentWrapper
        }

        val notification = CZNotificationEntity(
            generationID(),
            belongPkgName,
            icon = iconBmp,
            title = title,
            intentWrapper = intentWrapper,
            removeAfterExecution = removeAfterExecution
        )
        _showingListFlow.value += notification
        return notification.id
    }

    fun removeOneCZNotification(id: Int) {
        logTagD(TAG, "removeOneCZNotification id: $id")
        _showingListFlow.value = _showingListFlow.value.filter { it.id != id }
    }

    fun clearAllPkgNotification(pkgName: String) {
        logTagD(TAG, "clearAllPkgNotification: $pkgName")
        _showingListFlow.value = _showingListFlow.value.filter { it.belongPkgName != pkgName }
    }

    fun processNotification(entity: CZNotificationEntity) {
        if (entity.removeAfterExecution) {
            _showingListFlow.value = _showingListFlow.value.filter { it.id != entity.id }
        }
        val intent = entity.intentWrapper.toIntent().newTask()
        globalAppCtx.startActivity(intent)
    }

    private fun generationID(): Int {
        return idOffset++
    }
}

