package com.czur.starry.device.launcher.notice

import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import com.czur.czurutils.base.CZURContentProvider
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.cznotification.CZNotificationHandler
import com.czur.starry.device.baselib.cznotification.CZNotificationHandler.Companion.CZ_NOTIFICATION_PROVIDER_AUTHORITY
import com.czur.starry.device.baselib.utils.CONTENT

/**
 * Created by 陈丰尧 on 2024/8/13
 */
private const val TAG = "CZNotificationProvider"

class CZNotificationProvider : CZURContentProvider() {
    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        when (selection) {
            CZNotificationHandler.CZ_DEL_WHERE_ID -> {
                CZNotificationManager.removeOneCZNotification(selectionArgs?.get(0)?.toInt() ?: -1)
            }
            CZNotificationHandler.CZ_DEL_WHERE_PKG_NAME -> {
                CZNotificationManager.clearAllPkgNotification(selectionArgs?.get(0) ?: "")
            }
        }
        return 0
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        val icon = values?.getAsByteArray(CZNotificationHandler.CZ_NOTIFICATION_KEY_ICON)
        val title = values?.getAsString(CZNotificationHandler.CZ_NOTIFICATION_KEY_TITLE) ?: ""
        val pendingIntent =
            values?.getAsByteArray(CZNotificationHandler.CZ_NOTIFICATION_KEY_PENDING_INTENT)
        val removeAfterExecution =
            values?.getAsBoolean(CZNotificationHandler.CZ_NOTIFICATION_KEY_REMOVE_AFTER_EXECUTION)
                ?: true
        val pkgName = values?.getAsString(CZNotificationHandler.CZ_NOTIFICATION_KEY_PKG_NAME) ?: ""
        if (icon?.isNotEmpty() == true && title.isNotEmpty() && pendingIntent?.isNotEmpty() == true && pkgName.isNotEmpty()) {
            val id = CZNotificationManager.addOneCZNotification(
                pkgName,
                icon,
                title,
                pendingIntent,
                removeAfterExecution
            )
            return Uri.Builder().scheme(CONTENT)
                .authority(CZ_NOTIFICATION_PROVIDER_AUTHORITY)
                .path(id.toString())
                .build()
        }
        return null
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }
}