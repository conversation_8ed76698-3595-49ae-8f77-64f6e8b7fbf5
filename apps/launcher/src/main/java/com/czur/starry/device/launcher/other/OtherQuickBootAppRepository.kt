package com.czur.starry.device.launcher.other

import android.content.Context
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.download.DownloadPublishInfo
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.launcher.data.bean.OtherQuickBootAppItem
import com.czur.starry.device.launcher.meeting.NetAppDoubleCache
import com.czur.starry.device.launcher.net.IMeetingAppMainlandService
import com.czur.starry.device.launcher.net.IMeetingAppOverseasService
import com.czur.starry.device.launcher.net.IMeetingAppService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/8/20
 */
class OtherQuickBootAppRepository(
    val context: Context,
    private val netMeetingAppTag: String,
    private val defNetAppList: List<OtherQuickBootAppItem>
) {
    companion object {
        private const val TAG = "OtherQuickBootAppManager"
    }

    init {
        logTagV(TAG, "加载tag:${netMeetingAppTag}")
    }

    private val netMeetingAppService: IMeetingAppService by lazy {
        when (Constants.starryHWInfo.salesLocale) {
            StarryDevLocale.Mainland -> HttpManager.getService<IMeetingAppMainlandService>(BASE_URL = Constants.APP_STORE_BASE_URL)
            StarryDevLocale.Overseas -> HttpManager.getService<IMeetingAppOverseasService>(BASE_URL = Constants.APP_STORE_BASE_URL)
        }
    }

    private val netAppDoubleCache = NetAppDoubleCache(context, netMeetingAppTag)

    // 网络文件的包名
    private var netAppPackages = emptySet<String>()
    private val downloadProgress = mutableMapOf<String, Int>()
    private val requestDownloadTime = mutableMapOf<String, Long>()


    /**
     * 获取网络视频会议列表
     */
    suspend fun getNetAppList(): List<OtherQuickBootAppItem> = withContext(Dispatchers.IO) {
        // 第一步读取缓存
        val cacheList = netAppDoubleCache.loadFromCache()
        if (cacheList != null) {
            // 从缓存中找到数据
            logTagD(TAG, "从缓存中获取网络会议App")
            return@withContext cacheList
        }

        val netAppList = netMeetingAppService.getOtherNetMeetingApps(tagCode = netMeetingAppTag)
        if (netAppList.isSuccess) {
            netAppList.bodyList.also {
                // 更新缓存
                logTagV(TAG, "更新缓存数据")
                netAppDoubleCache.saveCache(it)
            }
        } else {
            // 如果缓存里啥也没有, 那么就读取之前写死的数据
            netAppDoubleCache.loadCacheDataFocus() ?: defNetAppList
        }
    }.also {
        netAppPackages = it.map { item ->
            item.packageName
        }.toSet()
    }

    fun isNetMeetingPkg(packageName: String): Boolean = packageName in netAppPackages

    fun upgradeDownloadProcess(publishInfo: List<DownloadPublishInfo>) {
        downloadProgress.clear()
        publishInfo
            .filter {
                isNetMeetingPkg(it.pkgName) && it.process != null   // 这里的process有可能为空,因为是从别的进程中发过来的
            }
            .forEach {
                downloadProgress[it.pkgName] = it.process
            }
    }

    fun removeDownloadTermination(pkgName: String) {
        downloadProgress.remove(pkgName)
    }

    /**
     * 记录请求下载的时间
     */
    fun saveRequestDownloadTime(pkgName: String) {
        requestDownloadTime[pkgName] = System.currentTimeMillis()
    }

    fun getDownloadProgress(packageName: String): Int {
        return try {
            downloadProgress.getOrDefault(packageName, -1)
        } catch (tr: Throwable) {
            logTagW(TAG, "getDownloadProgress,package:$packageName", tr = tr)
            -1
        }
    }

    fun hasCache(): Boolean {
        return netAppDoubleCache.hasCache()
    }
}