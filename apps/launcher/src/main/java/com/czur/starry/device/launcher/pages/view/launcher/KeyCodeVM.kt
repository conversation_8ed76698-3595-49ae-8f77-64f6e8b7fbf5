package com.czur.starry.device.launcher.pages.view.launcher

import android.app.Application
import android.os.SystemClock
import android.view.KeyEvent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_NAV_BAR_STATUS
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.baselib.utils.data.StateFlowDelegate
import com.czur.starry.device.baselib.utils.prop.getIntSystemProp
import com.czur.starry.device.launcher.utils.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull

/**
 * Created by 陈丰尧 on 2/22/21
 */
class KeyCodeVM(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "KeyCodeVM"

        private const val SLIDE_GUIDE_TIME_MAX = 100    // 一共引导100次

        private const val VALUE_NAV_BAR_STATUS_HIDE = 0
        private const val VALUE_NAV_BAR_STATUS_SHOW = 1
    }

    private val _scrollTimeFlow = MutableStateFlow(0L)
    val scrollTimeFlow = _scrollTimeFlow.asStateFlow()

    private val navBarStatusFlow = MutableStateFlow(VALUE_NAV_BAR_STATUS_HIDE)
    private val slideUpGuideTimesFlow = MutableStateFlow(0)
    private var slideUpGuideTimes: Int by StateFlowDelegate(slideUpGuideTimesFlow)

    val showSlideUpGuideFlow =
        navBarStatusFlow.combine(slideUpGuideTimesFlow) { navBarStatus, slideUpTimes ->
            // 导航栏隐藏时 并且 引导次数小于最大值 时显示
            navBarStatus == VALUE_NAV_BAR_STATUS_HIDE && slideUpTimes < SLIDE_GUIDE_TIME_MAX
        }

    init {

        // 初始化引导用户上划次数
        launch {
            slideUpGuideTimes = getDSValue(DS_KEY_GUIDE_SLIDE_UP_TIMES, 0)
            logTagI(TAG, "读取引导次数:${slideUpGuideTimes}")
        }

    }

    fun updateScrollTime(time: Long = SystemClock.elapsedRealtime()) {
        _scrollTimeFlow.value = time
    }

    /**
     * 当用户成功触发上划操作
     * 注: 根据需求, 只有从第一页迁移到第二页的时候, 才应该调用这个方法
     *      其他情况, 比如 第二页到第三页 等, 均不算成功引导, 增加记录
     */
    fun onLauncherSlideUp() {
        if (slideUpGuideTimes > SLIDE_GUIDE_TIME_MAX) {
            // 不需要记录了
            return
        }
        slideUpGuideTimes++
        launch {
            logTagV(TAG, "记录引导次数:${slideUpGuideTimes}")
            setDSValue(DS_KEY_GUIDE_SLIDE_UP_TIMES, slideUpGuideTimes)
        }
    }


    /**
     * 刷新导航栏状态
     */
    fun refreshNavBarStats() {
        launch {
            val status = getIntSystemProp(KEY_LAUNCHER_NAV_BAR_STATUS, VALUE_NAV_BAR_STATUS_HIDE)
            logTagI(TAG, "读取导航栏状态:${status}")
            navBarStatusFlow.value = status
        }
    }
}