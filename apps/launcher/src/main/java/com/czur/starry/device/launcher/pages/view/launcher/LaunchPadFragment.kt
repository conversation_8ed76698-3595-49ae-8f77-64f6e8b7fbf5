package com.czur.starry.device.launcher.pages.view.launcher

import android.os.Bundle
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.doOnItemRightClick
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.widget.EmptyVH
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.LaunchPadApp
import com.czur.starry.device.launcher.databinding.FragmentLaunchPadBinding
import com.czur.starry.device.launcher.pages.view.dialog.BootGooglePlayHintFloating
import com.czur.starry.device.launcher.pages.view.dialog.showForceStopDialog
import com.czur.starry.device.launcher.utils.BootCheckResult
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.checkBeforeBootApp
import com.czur.starry.device.launcher.utils.saveNeverRemindGooglePlayHint
import com.czur.starry.device.launcher.widget.ShakeIV

/**
 * Created by 陈丰尧 on 2/20/21
 */
private const val SPAN_COUNT = 7
private const val TAG = "LaunchPadFragment"

class LaunchPadFragment : CZViewBindingFragment<FragmentLaunchPadBinding>() {
    companion object {
        private const val KEY_PAGE_INDEX = "pageIndex"
        fun getInstance(page: Int): LaunchPadFragment {
            val fragment = LaunchPadFragment()
            val arg = Bundle()
            arg.putInt(KEY_PAGE_INDEX, page)
            fragment.arguments = arg
            return fragment
        }
    }

    private val adapter = LaunchPadAdapter()
    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })
    private var page = 0

    private var uninstallFloat: DoubleBtnCommonFloat? = null
    private var forceStopFloat: DoubleBtnCommonFloat? = null
    override fun FragmentLaunchPadBinding.initBindingViews() {
        padRv.layoutManager = GridLayoutManager(requireContext(), SPAN_COUNT)
        padRv.closeDefChangeAnimations()
        padRv.adapter = adapter

        padRv.setOnClickListener {
            appsVM.changeToNormalMode()
        }

        padRv.doOnItemRightClick { vh, view ->
            if (vh is EmptyVH) {
                // 右键点击RecyclerView空白部分
                if (appsVM.uninstallMode) {
                    // 取消卸载模式
                    appsVM.changeToNormalMode()
                } else {
                    appsVM.changeToUninstallMode()  // 切换到卸载模式
                }
                return@doOnItemRightClick true
            }
            when (view.id) {
                R.id.itemAppIconIv,
                R.id.itemAppNameTv,
                    -> {
                    // 点击图标 不切换模式
                    if (!appsVM.uninstallMode) {
                        appsVM.changeToUninstallMode()  // 切换到卸载模式
                    } else {
                        appsVM.refreshResetTime()   // 但是刷新自动恢复的时间
                    }
                    true
                }

                else -> false
            }
        }

        padRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            when (view.id) {
                R.id.itemAppIconIv,
                R.id.itemAppNameTv,
                    -> {
                    val launchpadApp = adapter.getData(pos)
                    if (!launchpadApp.uninstallMode) {
                        bootApp(launchpadApp)
                    }
                    true
                }

                R.id.uninstallIv -> {
                    val launchpadApp = adapter.getData(pos)
                    appsVM.refreshResetTime()   // 重置一下无响应时间
                    showUninstallDialog(launchpadApp)
                    true
                }

                else -> {
                    appsVM.changeToNormalMode()
                    false
                }
            }

        }

    }

    /**
     * 启动App
     */
    private fun bootApp(launchpadApp: LaunchPadApp) {
        logTagD(TAG, "启动app:${launchpadApp.appInfo.appName}")
        when (val checkBeforeBootApp = checkBeforeBootApp(launchpadApp.appInfo)) {
            BootCheckResult.ConflictWithMeeting -> {
                toast(R.string.toast_conflict_with_meeting, launchpadApp.appInfo.appName)
            }

            is BootCheckResult.ConflictWithMic -> {
                logTagI(TAG, "启动app:${launchpadApp.appInfo.appName} 与麦克风冲突")
                showForceStopDialog(
                    requireContext(),
                    launchpadApp.appInfo,
                    checkBeforeBootApp.useMicProcessList.toMutableList()
                ) {
                    bootApp(launchpadApp)
                }
            }

            BootCheckResult.Success -> {
                bootApp(pkgName = launchpadApp.appInfo.pkgName, context = requireContext())
            }

            BootCheckResult.HintGooglePlay -> {
                BootGooglePlayHintFloating { neverRemind, floating ->
                    floating.dismiss()
                    if (neverRemind){
                        launch {
                            saveNeverRemindGooglePlayHint()
                        }
                    }
                    bootApp(pkgName = launchpadApp.appInfo.pkgName, context = requireContext())
                }.show()
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        page = arguments?.getInt(KEY_PAGE_INDEX, 0) ?: 0

        appsVM.launchPadAppLive.observe(this) {
            val apps = appsVM.getApps(page)
            adapter.setData(apps)
        }

        appsVM.uninstallModeLive.observe(this) {
            doWithoutCatch {
                uninstallFloat?.dismiss()
            }
        }
    }

    /**
     * 显示卸载确认Dialog
     */
    private fun showUninstallDialog(launchPadApp: LaunchPadApp) {
        uninstallFloat = DoubleBtnCommonFloat(
            content = getString(
                R.string.dialog_msg_uninstall,
                launchPadApp.appInfo.appName
            ),
            outSideDismiss = true
        ) { commonFloat, position ->
            commonFloat.dismiss()
            if (position == 1) {
                appsVM.unInstallApp(launchPadApp.appInfo)
            }
        }.apply {
            setOnDismissListener {
                uninstallFloat = null
                appsVM.refreshResetTime()
            }
            show()
        }
    }


    private inner class LaunchPadAdapter : RecyclerView.Adapter<BaseVH>() {

        private val itemCallback = object : DiffUtil.ItemCallback<LaunchPadApp>() {
            override fun areItemsTheSame(oldItem: LaunchPadApp, newItem: LaunchPadApp): Boolean {
                return oldItem.appInfo.pkgName == newItem.appInfo.pkgName && oldItem.appInfo.appName == oldItem.appInfo.appName
            }

            override fun areContentsTheSame(oldItem: LaunchPadApp, newItem: LaunchPadApp): Boolean {
                return oldItem == newItem
            }

        }

        private val differ = AsyncListDiffer(this, itemCallback)

        fun setData(data: List<LaunchPadApp>) {
            differ.submitList(data)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
            return BaseVH(R.layout.item_app_pad, parent)
        }

        override fun onBindViewHolder(holder: BaseVH, position: Int) {
            val appInfo = differ.currentList[position]
            holder.setText(appInfo.appInfo.appName, R.id.itemAppNameTv)
            holder.setImgDrawable(appInfo.appInfo.copyDrawable, R.id.itemAppIconIv)
            holder.visible(appInfo.showUninstall, R.id.uninstallIv)

            val shakeIv = holder.getView<ShakeIV>(R.id.itemAppIconIv)
            if (appInfo.uninstallMode) {
                shakeIv.startShake()
            } else {
                shakeIv.stopShake()
            }
        }

        override fun getItemCount() = differ.currentList.size

        fun getData(pos: Int): LaunchPadApp = differ.currentList[pos]

    }

    override fun onPause() {
        super.onPause()
        forceStopFloat?.dismiss()
    }
}