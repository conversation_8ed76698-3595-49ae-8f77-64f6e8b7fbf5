package com.czur.starry.device.launcher.pages.view.launcher

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.FragmentLauncherMainBinding
import com.czur.starry.device.launcher.pages.view.main.meeting.MeetingFragment
import com.czur.starry.device.launcher.pages.view.main.quickboot.FileQuickBootFragment
import com.czur.starry.device.launcher.pages.view.main.quickboot.SettingsQuickBootFragment
import com.czur.starry.device.launcher.pages.view.main.quickboot.StarryPadQuickBootFragment
import com.czur.starry.device.launcher.pages.view.main.screenshare.ScreenShareInfoFragment
import com.czur.starry.device.launcher.pages.view.main.special.SpecialFeatureFragment
import com.czur.starry.device.launcher.pages.view.main.toolsheet.ToolSheetFragment
import com.czur.starry.device.sharescreen.esharelib.util.checkAndActiveEShare
import kotlinx.coroutines.delay
import kotlin.time.Duration.Companion.seconds

/**
 * Created by 陈丰尧 on 2/19/21(改)
 */
private const val ANIM_DURATION = 100L
private const val ANIM_SCALE_RATIO = 1.07f

class LauncherMainFragment : CZViewBindingFragment<FragmentLauncherMainBinding>() {
    companion object {
        private const val TAG = "LauncherMainFragment"
    }

    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })


    override fun FragmentLauncherMainBinding.initBindingViews() {
        launch {
            checkAndActiveEShare(requireContext())
        }

        fun moveToAppPad() {
            (activity as? LauncherMainActivity)?.moveToAppPadPage()
        }

        slideUpIv.setOnDebounceClickListener {
            // 点击时上划
            moveToAppPad()
        }

        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            slideUpTv.setText(R.string.str_slide_up_hint_simple)
        }
        slideUpTv.setOnDebounceClickListener {
            moveToAppPad()
        }


        childFragmentManager.commit {
            // 无线投屏
            replace(
                R.id.screenShareFragmentContainer,
                ScreenShareInfoFragment(),
                ScreenShareInfoFragment::class.java.simpleName
            )

            // 会议
            replace(
                R.id.meetingFragmentContainer,
                MeetingFragment(),
                MeetingFragment::class.java.simpleName
            )

            // 常用应用
            replace(
                R.id.toolSheetFragmentContainer,
                ToolSheetFragment(),
                ToolSheetFragment::class.java.simpleName
            )

            // 特色功能
            replace(
                R.id.specialFeatureFragmentContainer,
                SpecialFeatureFragment(),
                SpecialFeatureFragment::class.java.simpleName
            )

            // 快速启动
            replace(
                R.id.quickBootAppFragmentContainer1,
                StarryPadQuickBootFragment(),
                StarryPadQuickBootFragment::class.java.simpleName
            )
            replace(
                R.id.quickBootAppFragmentContainer2,
                FileQuickBootFragment(),
                FileQuickBootFragment::class.java.simpleName
            )
            replace(
                R.id.quickBootAppFragmentContainer3,
                SettingsQuickBootFragment(),
                SettingsQuickBootFragment::class.java.simpleName
            )
        }
        // 无线投屏
        screenShareFragmentContainer.setOnHoverListener { v, event ->
            screenShareFragmentContainer.pivotX = screenShareFragmentContainer.width / 2F
            screenShareFragmentContainer.pivotY = screenShareFragmentContainer.height.toFloat()

            v.doOnHover(event)
            false
        }

        // 视频会议
        meetingFragmentContainer.setOnHoverListener { v, event ->
            meetingFragmentContainer.pivotX = meetingFragmentContainer.width / 2F
            meetingFragmentContainer.pivotY = 0F

            v.doOnHover(event)
            false
        }

        // 常用
        toolSheetFragmentContainer.setOnHoverListener { v, event ->
            toolSheetFragmentContainer.pivotX = toolSheetFragmentContainer.width.toFloat()
            toolSheetFragmentContainer.pivotY = toolSheetFragmentContainer.height.toFloat()

            v.doOnHover(event)
            false
        }

        // StarryPad
        quickBootAppFragmentContainer1.setOnHoverListener { v, event ->
            quickBootAppFragmentContainer1.pivotX = quickBootAppFragmentContainer1.width.toFloat()
            quickBootAppFragmentContainer1.pivotY = 0F

            v.doOnHover(event)
            false
        }

        // 文件
        quickBootAppFragmentContainer2.setOnHoverListener { v, event ->
            quickBootAppFragmentContainer2.pivotX = quickBootAppFragmentContainer1.width / 2F
            quickBootAppFragmentContainer2.pivotY = 0F

            v.doOnHover(event)
            // 将左右两边View需要"挤"一下
            when (event.action) {
                MotionEvent.ACTION_HOVER_ENTER -> {
                    quickBootAppFragmentContainer1.transX(-7F)
                    quickBootAppFragmentContainer3.transX(7F)
                }

                MotionEvent.ACTION_HOVER_EXIT -> {
                    quickBootAppFragmentContainer1.transX(0F)
                    quickBootAppFragmentContainer3.transX(0F)
                }
            }
            false
        }

        // 设置
        quickBootAppFragmentContainer3.setOnHoverListener { v, event ->
            quickBootAppFragmentContainer3.pivotX = 0F
            quickBootAppFragmentContainer3.pivotY = 0F

            v.doOnHover(event)
            false
        }
    }

    private fun View.doOnHover(event: MotionEvent) {
        when (event.action) {
            // 进入时放大
            MotionEvent.ACTION_HOVER_ENTER -> scaleUp(this)
            // 离开时缩小
            MotionEvent.ACTION_HOVER_EXIT -> scaleDown(this)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        launch {
            // 延时显示, 避免刚进入页面时, 由于处理模糊图片导致UI问题
            delay(2.seconds)
            binding.rootContainerView.show()
        }

        repeatCollectOnResume(keyCodeVM.showSlideUpGuideFlow) { showGuide ->
            if (showGuide) {
                logTagV(TAG, "显示引导UI")
                binding.slideUpGuideGroup.show()
            } else {
                // 隐藏引导UI
                logTagV(TAG, "隐藏引导UI")
                binding.slideUpGuideGroup.gone()
            }
        }
    }

    private fun View.transX(x: Float) {
        animate().translationX(x)
            .setDuration(ANIM_DURATION)
            .start()

    }

    /**
     * 放大
     */
    private fun scaleUp(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", ANIM_SCALE_RATIO)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", ANIM_SCALE_RATIO)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * 缩小
     */
    private fun scaleDown(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }


}