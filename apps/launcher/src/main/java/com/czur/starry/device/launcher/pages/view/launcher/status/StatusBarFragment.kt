package com.czur.starry.device.launcher.pages.view.launcher.status

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.view.MotionEvent
import android.view.View
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_CENTER
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_SETTING
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_PERSONAL
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_TRANSCRIPTION
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_VOICE_ASSISTANT
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantEnabled
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantEnabledLive
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantSupport
import com.czur.starry.device.baselib.data.provider.VoiceAssistantHandler.isVoiceAssistantSupportLive
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toastFailCheckNet
import com.czur.starry.device.baselib.view.floating.DisplayPeripheralModeType
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.FragmentStatusBarBinding
import com.czur.starry.device.launcher.notice.CZNotificationEntity
import com.czur.starry.device.launcher.notice.CZNotificationManager
import com.czur.starry.device.launcher.pages.view.main.ShareViewModel
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.utils.isServiceRunning
import com.czur.starry.device.noticelib.ACTION_TYPE_POSITIVE
import com.czur.starry.device.noticelib.NotifyMsgHandler
import com.czur.starry.device.noticelib.floating.BtnFloatNotice
import com.czur.starry.device.noticelib.floating.NotifyMsg
import com.czur.starry.device.noticelib.floating.SimpleFloatNotice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2/20/21
 */

private const val TAG = "StatusBarFragment"

private const val MEETING_ROOM_NAME_DATA = "meetingRoomNameData"
private const val MEETING_ROOM_NAME = "meetingRoomName"
private const val SHOW_THRESHOLD = 150L
private const val ROOM_NAME_SHOW_LOGO = "nullShowLogo"

class StatusBarFragment : CZViewBindingFragment<FragmentStatusBarBinding>() {
    private val shareViewModel: ShareViewModel by viewModels({ requireActivity() })

    // 滚轮滚动
    var onVScrollListener: ((axisValue: Float) -> Unit)? = null

    // 外设模式退出弹窗
    private var peripheralModeExitFloating: DoubleBtnCommonFloat? = null

    override fun FragmentStatusBarBinding.initBindingViews() {
        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            notifyBarUserIv.gone()
            notifyBarUserTv.gone()
        } else {
            notifyBarUserIv.show()
            notifyBarUserTv.show()
        }

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagV(TAG, "军工版本")
            // 去掉个人中心
            notifyBarUserIv.gone()
            notifyBarUserTv.gone()
        }

        personalCenterGroup.setOnDebounceClickListener {
            bootAppByAction(ACTION_BOOT_PERSONAL){
                putExtra("BootTimeStamp", System.currentTimeMillis())
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            }   // 个人中心模块
        }
        notifyBarMsgIv.setOnDebounceClickListener {
            logTagV(TAG, "点击通知消息")
            bootAppByAction(ACTION_BOOT_NOTICE_CENTER)   // 通知消息模块
        }

        notifyBarWifiIv.onNetViewClick = {
            logTagV(TAG, "点击网络状态")
            // 启动网络设置页面
            bootSettingNet(it)
        }

        // 滚轮事件
        statusBgLayout.setOnGenericMotionListener { _, event ->
            if (event?.action == MotionEvent.ACTION_SCROLL) {
                // 拦截鼠标滚轮事件, 将其转化为viewPager的翻页事件
                val v = event.getAxisValue(MotionEvent.AXIS_VSCROLL)
                onVScrollListener?.invoke(v)
                return@setOnGenericMotionListener true
            }
            false
        }

        byomInUseLayout.setOnDebounceClickListener {
            logTagV(TAG, "点击BYOM")
            showPeripheralModeExitFloating()
        }

        isVoiceAssistantSupportLive.observe(this@StatusBarFragment) {
            updateVoiceAssistantEnable()
        }

        isVoiceAssistantEnabledLive.observe(this@StatusBarFragment) {
            updateVoiceAssistantEnable()
        }

        repeatOnResume {
            logTagD(TAG, "刷新外设模式")
            shareViewModel.refreshGadgetState()
        }

        czNotificationLayout.setOnDebounceClickListener {
            val notifyMsg = czNotificationLayout.tag as? CZNotificationEntity
                ?: return@setOnDebounceClickListener
            CZNotificationManager.processNotification(notifyMsg)
        }

        starryHubLogoIv.setOnDebounceClickListener {
            // Studio与Q1 功能一致
            if (canChangeLogo()) {
                doRename(getMeetingRoomNameData())
            }
        }

        aiCCPlaceHolderView.setOnDebounceClickListener {
            logTagI(TAG, "点击AI互译按钮")
            if (TransHandler.isTranslating) {
                // 正在录制中, 显示AI字幕
                TransHandler.showSubtitles = true
            } else {
                bootAppByAction(ACTION_BOOT_TRANSCRIPTION)
            }
        }

        aiCCIvBtn.setOnDebounceClickListener {
            logTagI(TAG, "点击AI互译按钮--结束翻译")
            // 检查字幕服务是否存在
            if (isServiceRunning(
                    requireContext(),
                    "com.czur.starry.device.transcription.service.SubtitleTransAlertWindowService"
                )
            ) {
                // 服务存在，正常停止翻译
                logTagI(TAG, "点击AI互译按钮--结束翻译")
                TransHandler.stopTrans = true
            } else {
                // 服务不存在（可能已崩溃），直接设置翻译状态为false
                logTagI(TAG, "字幕服务不存在，直接设置翻译状态为false")
                TransHandler.isTranslating = false
            }
        }

        meetingRoomNameTV.setOnDebounceClickListener {
            doRename(getMeetingRoomNameData())
        }

        if (Constants.starryHWInfo.hasTouchScreen) {
            voiceAssistantIv.gone()
        }
        voiceAssistantIv.setOnClickListener {
            logTagV(TAG, "点击语音助手")
            // 启动语音助手
            val intent = Intent().apply {
                `package` = "com.czur.starry.device.voiceassistant"
                action = ACTION_BOOT_VOICE_ASSISTANT
                putExtra("key_message", "wake_up")
            }
            globalAppCtx.startService(intent)

        }
    }

    /**
     * 更新语音助手是否可用
     */
    private fun updateVoiceAssistantEnable() {
        logTagV(TAG, "updateVoiceAssistantEnable")
        viewLifecycleOwner.lifecycleScope.launch {
            if (isVoiceAssistantSupport && isVoiceAssistantEnabled) {
                binding.voiceAssistantIv.setImageDrawable(
                    ResourcesCompat.getDrawable(
                        resources,
                        R.drawable.icon_voice_assistant,
                        null
                    )
                )
            } else {
                binding.voiceAssistantIv.setImageDrawable(
                    ResourcesCompat.getDrawable(resources, R.drawable.icon_voice_disable, null)
                )
            }
        }
    }


    /**
     * 是否可以更改logo
     */
    private fun canChangeLogo(): Boolean {
        return when (val s = Constants.starryHWInfo.series) {
            is Q1Series -> when (s.model) {
                StarryModel.Q1Model.Q1 -> false
                else -> true
            }

            is Q2Series -> when (s.model) {
                StarryModel.Q2Model.Q2 -> false
                StarryModel.Q2Model.Q2Pro -> false
                else -> true
            }

            is StudioSeries -> when (s.model) {
                StarryModel.StudioModel.Studio -> false
                StarryModel.StudioModel.StudioPro -> false
                else -> true
            }
        }
    }

    /**
     * 显示外设模式退出浮窗
     */
    private fun showPeripheralModeExitFloating() {
        logTagV(TAG, "showPeripheralModeExitFloating")
        val usbRunning = shareViewModel.peripheralUSBRunning
        var contentStr = getString(R.string.floating_peripheral_byom_exit_content)
        var peripheralModeType = DisplayPeripheralModeType.BYOM
        if (usbRunning) {
            contentStr = getString(R.string.floating_peripheral_usb_exit_content)
            peripheralModeType = DisplayPeripheralModeType.USB
        }

        peripheralModeExitFloating = DoubleBtnCommonFloat(
            content = contentStr,
            peripheralModeType = peripheralModeType
        ) { commonFloat, position ->
            commonFloat.dismiss()
            when (position) {
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {}
                DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                    viewLifecycleScope.launch {
                        withLoading {
                            if (usbRunning) {
                                shareViewModel.stopUSBMode()
                            } else {
                                shareViewModel.stopByom()
                            }
                        }
                    }
                }
            }
        }.apply {
            show()
            setOnDismissListener {
                peripheralModeExitFloating = null
            }
        }


    }

    /**
     * 无网络/wifi     启动WiFi页面
     * 有线网络        启动有线网页面
     */
    private fun bootSettingNet(netStatus: NetStatusUtil.NetStatus) {
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            bootAppByAction(ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "wiredNet")
            }
            return
        }
        when (netStatus) {
            NetStatusUtil.NetStatus.NO_NET_WORK,
            NetStatusUtil.NetStatus.WIFI -> bootAppByAction(ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "wifi")
            }

            NetStatusUtil.NetStatus.ETHERNET -> bootAppByAction(ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "wiredNet")
            }
        }
    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // 是否有消息
        NotifyMsgHandler.hasUnReadMsgLive.observe(this) { hasUnReadMsg ->
            logTagV(TAG, "hasUnReadMsg:${hasUnReadMsg}")
            binding.notifyBarUnReadPoint.visibility =
                if (hasUnReadMsg && UserHandler.isLogin) View.VISIBLE else View.GONE
        }

        // 登录状态
        UserHandler.isLoginLive.observe(this) {
            // 登录状态改变, 都去刷新一次UI
            binding.notifyBarUnReadPoint.visibility =
                if (NotifyMsgHandler.hasUnReadMsgLive.value == true && UserHandler.isLogin) View.VISIBLE else View.GONE
            updateUserName(it)

        }

        // 接收新消息通知
        NoticeHandler.register(MsgType(MsgType.NOTIFY_MSG, MsgType.NOTIFY_MSG_LATEST), this) {
            logTagV(TAG, "Launcher收到了新消息")
            launch {
                parsNewNotice(it)
            }

        }

        TransHandler.isTranslatingLive.observe(this) {
            logTagI(TAG, "AI互译字幕开启状态:$it")
            if (!it) {
                binding.aiCCGroup.gone()
            }
        }

        TransHandler.showSubtitlesLive.observe(this) {
            logTagI(TAG, "AI互译字幕显示状态:$it")
            if (TransHandler.isTranslating) {
                if (it) {
                    binding.aiCCGroup.gone()
                } else {
                    binding.aiCCGroup.show()
                    Glide.with(this)
                        .load(R.raw.aicc_animation_25fps_lossless)
                        .diskCacheStrategy(DiskCacheStrategy.DATA)
                        .format(DecodeFormat.PREFER_ARGB_8888)
                        .into(binding.aiCCAnimIv)

                    // aiCCGroup显示时，获取aiCCPlaceHolderView的位置信息
                    updateAiccPlaceholderPosition()
                }
            }
        }

        // 外设模式相关
        repeatCollectOnResume(shareViewModel.peripheralByomRunningFlow) {
            if (it) {
                binding.peripheralModeNameTv.setText(R.string.btn_status_bar_peripheral_byom_in_use)
            } else if (shareViewModel.peripheralUSBRunning) {
                binding.peripheralModeNameTv.setText(R.string.btn_status_bar_peripheral_usb_in_use)
            }
        }
        repeatCollectOnResume(shareViewModel.peripheralUSBRunningFlow) {
            if (it) {
                binding.peripheralModeNameTv.setText(R.string.btn_status_bar_peripheral_usb_in_use)
            } else if (shareViewModel.peripheralByomRunning) {
                binding.peripheralModeNameTv.setText(R.string.btn_status_bar_peripheral_byom_in_use)
            }
        }

        repeatCollectOnResume(shareViewModel.peripheralModeRunningFlow) { mode ->
            logTagV(TAG, "byomAndUsbRunning:$mode")
            val status = mode.byom || mode.usb
            binding.byomInUseLayout.gone(!status)
            if ((peripheralModeExitFloating?.peripheralModeType == DisplayPeripheralModeType.BYOM && mode.usb) ||
                (peripheralModeExitFloating?.peripheralModeType == DisplayPeripheralModeType.USB && mode.byom) ||
                !status
            ) {
                peripheralModeExitFloating?.dismiss()
            }
        }

        // 通知信息
        repeatCollectOnResume(CZNotificationManager.showingFlow) {
            if (it != null) {
                binding.czNotificationLayout.show()
                binding.czNotificationIconIv.setImageBitmap(it.icon)
                Html.fromHtml(it.title, Html.FROM_HTML_MODE_LEGACY).let { title ->
                    binding.czNotificationTitleTv.text = title
                }
                binding.czNotificationLayout.tag = it
            } else {
                binding.czNotificationLayout.gone()
                binding.czNotificationIconIv.setImageBitmap(null)
                binding.czNotificationTitleTv.text = ""
                binding.czNotificationLayout.tag = null
            }
        }
    }

    private suspend fun parsNewNotice(msg: NoticeMsg) = withContext(Dispatchers.IO) {
        val notifyMsg = NotifyMsg.create(msg)
        synchronized(this) {
            if (notifyMsg.hasShown()) {
                logTagV(TAG, "消息已经显示, 不再显示")
                return@withContext
            }
        }
        notifyMsg.saveShown()
        if (System.currentTimeMillis() - notifyMsg.receiveTime < SHOW_THRESHOLD) {
            // 消息到达的时间,和当前时间的差距,只有小于阈值时,才显示
            // 即当消息到达时, 当前页面不是Launcher,则不显示消息
            showNotifyMsg(notifyMsg)
        } else {
            logTagW(TAG, "消息到达时间超过阈值, 不显示")
        }

    }

    private fun updateUserName(isLogin: Boolean) {
        if (isLogin) {
            binding.notifyBarUserTv.text = ""
        } else {
            binding.notifyBarUserTv.setText(com.czur.starry.device.baselib.R.string.str_notify_bar_not_login)
        }
    }

    /**
     * 显示消息浮窗
     */
    private suspend fun showNotifyMsg(notifyMsg: NotifyMsg) = withContext(Dispatchers.Main) {
        logTagD(TAG, "showNotifyMsg")
        if (NotifyMsgHandler.notifyMsgHasAction(notifyMsg.type)) {
            // 展示带按钮的消息
            BtnFloatNotice(notifyMsg) {
                logTagV(TAG, "点击同意")
                requireActivity().launch {
                    val res = NotifyMsgHandler.processNotifyMsg(
                        requireContext(),
                        notifyMsg.id,
                        notifyMsg.type,
                        ACTION_TYPE_POSITIVE,//积极的
                        notifyMsg.data
                    )
                    if (!res) {
                        logTagW(TAG, "加入企业res:${res}")
                        toastFailCheckNet()
                    }
                }
            }.show()
        } else {
            // 展示不带按钮的消息
            SimpleFloatNotice(msg = notifyMsg.title).show()
        }
    }

    //文件重命名
    @SuppressLint("StringFormatInvalid")
    private fun doRename(name: String) {
        SettingUtil.PersonalizationSetting.showRenameActivityDialog(requireContext(), name)
    }

    private fun saveMeetingRoomNameData(name: String) {
        val sharedPref =
            activity?.getSharedPreferences(MEETING_ROOM_NAME_DATA, Context.MODE_PRIVATE)
        if (sharedPref != null) {
            with(sharedPref.edit()) {
                putString(MEETING_ROOM_NAME, name)
                apply()
            }
        }
    }

    private fun getMeetingRoomNameData(): String {
        val sharedPref =
            activity?.getSharedPreferences(MEETING_ROOM_NAME_DATA, Context.MODE_PRIVATE)
        val usedName = sharedPref?.getString(MEETING_ROOM_NAME, "").orEmpty()
        return usedName
    }

    override fun onResume() {
        super.onResume()
        launch {
            val logoTv = SettingUtil.PersonalizationSetting.getLauncherMeetingRoomName()
            if (!logoTv.isNullOrEmpty()) {//非空即非重启
                if (logoTv == ROOM_NAME_SHOW_LOGO) {
                    saveMeetingRoomNameData("")
                } else {
                    saveMeetingRoomNameData(logoTv)
                }
            }
            refreshMeetingRoomNameUI()
        }
    }

    private fun refreshMeetingRoomNameUI() {
        val meetingRoomName = getMeetingRoomNameData()
        if (meetingRoomName.isNotEmpty()) {
            binding.meetingRoomNameTV.text = meetingRoomName
            binding.meetingRoomNameTV.show()
            binding.starryHubLogoIv.gone()
        } else {
            binding.meetingRoomNameTV.gone()
            binding.starryHubLogoIv.show()
        }
    }

    /**
     * 更新aiCCPlaceHolderView的位置信息
     * 在aiCCGroup显示时调用，获取aiCCPlaceHolderView在屏幕上的中心点位置
     */
    private fun updateAiccPlaceholderPosition() {
        // 使用post确保View已经完成布局
        binding.aiCCPlaceHolderView.post {
            val location = IntArray(2)
            binding.aiCCPlaceHolderView.getLocationOnScreen(location)
            val viewX = location[0]
            val viewWidth = binding.aiCCPlaceHolderView.width
            val viewHeight = binding.aiCCPlaceHolderView.height

            // 计算View的中心点坐标
            val centerX = viewX + viewWidth / 2
            val centerY = 0

            logTagI(TAG, "aiCCPlaceHolderView位置: 左上角($viewX, 0), 尺寸(${viewWidth}x${viewHeight}), 中心点($centerX, $centerY)")

            // 更新中心点坐标到TransHandler中，供transcription应用使用
            TransHandler.updateAiccPlaceholderPosition(centerX, centerY)
        }
    }
}

private val hasShownMsgIds = mutableSetOf<Long>()

private fun NotifyMsg.hasShown(): Boolean {
    return id in hasShownMsgIds
}

private fun NotifyMsg.saveShown() {
    hasShownMsgIds.add(id)
}


