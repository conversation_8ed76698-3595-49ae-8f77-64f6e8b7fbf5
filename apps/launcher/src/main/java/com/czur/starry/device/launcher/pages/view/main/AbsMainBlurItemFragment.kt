package com.czur.starry.device.launcher.pages.view.main

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.viewbinding.ViewBinding
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.widget.BlurImageView

/**
 * Created by 陈丰尧 on 2025/7/3
 */

abstract class AbsMainBlurItemFragment<VB : ViewBinding> : CZViewBindingFragment<VB>() {
    abstract val blurIv: BlurImageView
    protected val keyCodeVM: KeyCodeVM by activityViewModels()

    final override fun VB.initBindingViews() {
        blurIv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(blurIv)
            }
        }
        initSubViews()
    }

    open fun VB.initSubViews() {
        // 子类可以重写此方法来初始化子视图
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropManager.BackdropEvent.CHANGE) {
                blurIv.post {
                    launch {
                        BackdropManager.setBlurBitmapToBlurIv(blurIv)
                    }
                }
            }
        }
        repeatCollectOnStart(keyCodeVM.scrollTimeFlow) {
            blurIv.invalidate()
        }
    }
}