package com.czur.starry.device.launcher.pages.view.main

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.hdmilib.HDMIIFStatus
import com.czur.starry.device.hdmilib.HDMIMonitor
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Created by 陈丰尧 on 2022/8/22
 */
class HDMIViewModel(application: Application) : AndroidViewModel(application) {
    private val hdmiMonitor = HDMIMonitor()

    private val _hdmiStatusFlow = MutableStateFlow(hdmiMonitor.getHDMIIFStatus())
    val hdmiStatusFlow = _hdmiStatusFlow.asStateFlow()
    val hdmiStatus: HDMIIFStatus
        get() = _hdmiStatusFlow.value


    init {
        hdmiMonitor.registerHDMIReceiver(appContext) {
            _hdmiStatusFlow.value = it
        }
    }

    override fun onCleared() {
        hdmiMonitor.unRegisterHDMIReceiver(appContext)
        super.onCleared()
    }
}