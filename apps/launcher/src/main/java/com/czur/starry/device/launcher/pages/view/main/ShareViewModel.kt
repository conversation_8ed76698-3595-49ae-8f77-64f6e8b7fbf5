package com.czur.starry.device.launcher.pages.view.main

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.asFlow
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy.USBModeState
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.launcher.utils.BootUtilValues
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_AUDIO_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_CAMERA_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_DEVICE_NAME
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.eshare.serverlibrary.api.EShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.withContext


private const val TAG = "ShareViewModel"

data class PeripheralMode(val byom: Boolean, val usb: Boolean)

class ShareViewModel(application: Application) : AndroidViewModel(application) {
    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(application)
    }

    /**
     * 用于显示的PinCode
     */
    val wifiSSIDNameFlow
        get() = netStatusUtil.wifiSSIDNameLive.asFlow()
    val netStatusFlow
        get() = netStatusUtil.netStatusFlow
    val netDisplayFlow by lazy {
        combine(wifiSSIDNameFlow,netStatusFlow){name,status ->
            name to status
        }
    }

    /**
     * 显示的无线投屏名称
     */
    private val _showShareNameFlow = MutableStateFlow("")
    val showShareNameFlow = _showShareNameFlow.asStateFlow()

    // 宜享BYOM的状态
    private val _peripheralByomRunningFlow = MutableStateFlow(false)
    val peripheralByomRunningFlow = _peripheralByomRunningFlow.asStateFlow()
    val peripheralByomRunning: Boolean
        get() = _peripheralByomRunningFlow.value

    // USB外设模式的状态
    private val _peripheralUSBRunningFlow = MutableStateFlow(false)
    val peripheralUSBRunningFlow = _peripheralUSBRunningFlow.asStateFlow()
    val peripheralUSBRunning: Boolean
        get() = _peripheralUSBRunningFlow.value

    // 外设模式是否运行中
    val peripheralModeRunningFlow =
        combine(_peripheralByomRunningFlow, _peripheralUSBRunningFlow) { byom, usb ->
            logTagV(TAG, "peripheralModeRunningFlow byom = $byom, usb = $usb")
            PeripheralMode(byom, usb)
        }

    private val eShareCallback: EShareCallback = object : SimpleEShareCallback() {
        override fun onSettingsChanged(key: String, newValue: Any?) {
            logTagD(TAG, "eShareCallback key = ${key}")
            when (key) {
                E_SHARE_DEVICE_NAME -> {
                    _showShareNameFlow.value = newValue?.toString() ?: ""
                }

                E_SHARE_BYOM_CAMERA_RUNNING, E_SHARE_BYOM_AUDIO_RUNNING -> {
                    _peripheralByomRunningFlow.value = eShareServerSDK.isBYOMRunning
                    logTagD(TAG, "eShareCallback isByomrunning = ${eShareServerSDK.isBYOMRunning}")
                }

                else -> {}
            }
        }
    }

    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(application)
    }

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy { category, eventID, para1, extend1, extend2 ->
            logTagD(
                TAG,
                "category = $category, eventID = $eventID, para1 = $para1, extend1 = $extend1, extend2 = $extend2"
            )
            if (category == 3) {
                launch {
                    refreshGadgetState()
                }
            }
        }
    }

    private val noticeListener: (msg: NoticeMsg) -> Unit = {
        logTagD(TAG, "gadgetState改变, 刷新USB外设模式")
        launch {
            refreshGadgetState()
        }
    }

    init {
        launch {
            eShareServerSDK.registerCallback(eShareCallback)
            loadShareInfo()
        }

        launch {
            refreshGadgetState()   // 刷新USB外设模式
        }

        launch {
            peripheralUSBRunningFlow.collect {
                logTagD(TAG, "peripheralUSBRunningFlow = $it")
                if (it && _peripheralByomRunningFlow.value) {
                    _peripheralByomRunningFlow.value = false
                }
                BootUtilValues.peripheralUSBRunning = it    // 赋值给BootUtil,方便启动App时判断
            }
        }

        netStatusUtil.startWatching()
        _peripheralByomRunningFlow.value = eShareServerSDK.isBYOMRunning  // 初始值

        registerNotice()
    }

    private fun registerNotice() {
        NoticeHandler.register(
            MsgType(MsgType.SYNC, MsgType.COMMON_PERIPHERAL_USB_CHANGE),
            listener = noticeListener
        )
    }

    /**
     * 刷新无线投屏信息
     */
    suspend fun loadShareInfo() = withContext(Dispatchers.IO) {
        refreshShareName()
    }

    /**
     * 刷新USB外设模式
     */
    suspend fun refreshGadgetState() = withContext(Dispatchers.IO) {
        logTagV(TAG, "refreshGadgetState")
        _peripheralUSBRunningFlow.value =
            systemManager.getGadgetMode().also {
                logTagV(TAG, "USB外设模式 = $it")
            } == USBModeState.USB_GADGET_STREAM_ON
    }

    /**
     * 刷新设备名称
     */
    private suspend fun refreshShareName() = withContext(Dispatchers.IO) {
        _showShareNameFlow.value = eShareServerSDK.deviceName ?: ""
    }

    /**
     *  打开EShareSDK
     */
    fun startShareService() {
        doWithoutCatch {
            logTagD(TAG, "Launcher启动, 打开EShare")
            eShareServerSDK.startEShareServer()
        }
    }

    /**
     * 关闭外设模式
     */
    suspend fun stopPeripheralMode() {
        logTagI(TAG, "stopPeripheralMode")
        if (_peripheralByomRunningFlow.value) {
            stopByom()
        }
        if (_peripheralUSBRunningFlow.value) {
            stopUSBMode()
        }
    }

    fun stopByom() {
        logTagV(TAG, "stopByom")
        eShareServerSDK.stopAllBYOM()
    }

    suspend fun stopUSBMode() {
        logTagV(TAG, "stopUSBMode")
        systemManager.setGadgetMode(false)
        logTagV(TAG, "关闭USBMode完成")
        refreshGadgetState()
    }

    fun isEShareActive(): Boolean {
        return eShareServerSDK.isEShareActivated
    }

    override fun onCleared() {
        super.onCleared()
        logTagD(TAG, "Launcher-ShareViewMode onCleared")

        NoticeHandler.unRegister(
            MsgType(MsgType.SYNC, MsgType.COMMON_PERIPHERAL_USB_CHANGE),
            noticeListener
        )
        netStatusUtil.stopWatching()
        eShareServerSDK.unregisterCallback(eShareCallback)
    }


}