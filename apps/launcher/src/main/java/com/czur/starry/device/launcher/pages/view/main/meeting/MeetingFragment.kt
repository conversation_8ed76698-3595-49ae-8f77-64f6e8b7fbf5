package com.czur.starry.device.launcher.pages.view.main.meeting

import android.graphics.Bitmap
import android.os.Bundle
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.fragment.app.activityViewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.FragmentMainMeetingBinding
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus
import com.czur.starry.device.launcher.meeting.OtherMeetingFloat
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.pages.view.main.AbsMainBlurItemFragment
import com.czur.starry.device.launcher.utils.addIconHoverAnim
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.widget.BlurImageView
import com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv

/**
 * Created by 陈丰尧 on 2025/7/3
 */
private const val TAG = "MeetingFragment"

class MeetingFragment : AbsMainBlurItemFragment<FragmentMainMeetingBinding>() {
    override val blurIv: BlurImageView
        get() = binding.bgBiv

    private val appsVM: AppInfoViewModel by activityViewModels()


    private val iconViews by lazy {
        listOf(
            NetAppIconView(
                binding.meetingAppIv1, binding.meetingAppTv1, binding.meetingApp1
            ),
            NetAppIconView(
                binding.meetingAppIv2, binding.meetingAppTv2, binding.meetingApp2
            ),
            NetAppIconView(
                binding.meetingAppIv3, binding.meetingAppTv3, binding.meetingApp3
            ),
            NetAppIconView(
                binding.meetingAppIv4, binding.meetingAppTv4, binding.meetingApp4
            ),
        )
    }

    override fun FragmentMainMeetingBinding.initSubViews() {
        markView.setDebounceTouchClickListener {
            // 弹出视频会议弹窗
            launch {
                try {
                    val bgImg = takeScreenShot(Bitmap.Config.ARGB_8888)?.blur(samplingRate = 4)
                    OtherMeetingFloat(
                        bgImg,
                        getString(R.string.float_title_other_net_meeting),
                        appsVM.otherNetMeetingAppsLive
                    ).show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    logTagE(TAG, "====${e}")
                }
            }
        }

        iconViews.forEachIndexed { index, app ->
            app.iconView.setDebounceTouchClickListener {
                bootOrInstallApp(index)
            }
            app.nameView.setDebounceTouchClickListener {
                logTagV(TAG, "点击应用名称:${app.nameView.text}")
                bootOrInstallApp(index)
            }
            app.iconView.addIconHoverAnim()
        }

        listOf(usbPeripheralsIv, byomWifiIv).forEach {
            it.addIconHoverAnim()
        }

        usbPeripheralsLayer.setDebounceTouchClickListener {
            bootAppByAction(BootParam.ACTION_BOOT_BYOM_GUIDE)

        }
        byomWifiLayer.setDebounceTouchClickListener {
            bootAppByAction(BootParam.ACTION_BOOT_BYOM_ESHARE_GUIDE)
        }

    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        appsVM.otherNetMeetingAppsLive.observe(this) { otherMeetingApps ->
            /*
            Launcher上的显示逻辑:
            1. 如果有已安装的应用, 则只显示已安装的应用
            2. 一个已安装应用都没有, 显示后台配置列表中的前三个
             */

            val showApps = otherMeetingApps.filter {
                it.status == NetMeetingAppStatus.INSTALL
            }.ifEmpty {
                // 没有安装的应用
                otherMeetingApps
            }.take(iconViews.size)        // 最多2个
            repeat(iconViews.size) { index ->
                val appView = iconViews[index]
                showApps.getOrNull(index)?.let {
                    appView.groupView.show()
                    appView.iconView.updateIcon(it)
                    appView.nameView.text = it.appName
                } ?: kotlin.run {
                    appView.groupView.invisible()
                }
            }
        }
    }

    /**
     * 启动对应的会议App
     */
    private fun bootOrInstallApp(index: Int) {
        val clickApp =
            appsVM.otherNetMeetingApps.getOrNull(index) ?: return   // 没有对应的App信息就直接Return
        when (clickApp.status) {
            NetMeetingAppStatus.INSTALL -> {
                logTagV(TAG, "启动App:${clickApp.appName}")
                bootApp(clickApp.pkgName)
            }

            NetMeetingAppStatus.UNINSTALL -> {
                if (clickApp.downloadProcess >= 0) {
                    logTagD(TAG, "正在下载, 不做任何操作")
                } else {
                    logTagD(TAG, "安装App:${clickApp.appName}")
                    appsVM.downloadNetMeetingApp(clickApp)
                }
            }
        }
    }


    /**
     * IconView
     */
    private data class NetAppIconView(
        val iconView: OtherQuickBootAppIconIv, val nameView: TextView, val groupView: Group
    )

}