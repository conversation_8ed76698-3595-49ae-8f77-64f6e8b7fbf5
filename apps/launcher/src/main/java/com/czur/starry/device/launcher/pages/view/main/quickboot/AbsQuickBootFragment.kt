package com.czur.starry.device.launcher.pages.view.main.quickboot

import android.os.Bundle
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.databinding.FragmentMainQuickBootBinding
import com.czur.starry.device.launcher.databinding.InflateQuickBootFileBinding
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.utils.bootAppByAction

/**
 * Created by 陈丰尧 on 2025/7/2
 */
abstract class AbsQuickBootFragment : CZViewBindingFragment<FragmentMainQuickBootBinding>() {
    private val keyCodeVM: KeyCodeVM by activityViewModels()

    @get:DrawableRes
    abstract val markViewBgRes: Int

    @get:DrawableRes
    abstract val iconIvRes: Int

    @get:StringRes
    abstract val appNameTvRes: Int

    abstract val bootAction: String

    protected val quickBootViewModel: QuickBootViewModel by viewModels()

    override fun FragmentMainQuickBootBinding.initBindingViews() {
        markView.setBackgroundResource(markViewBgRes)   // 背景
        appNameTv.setText(appNameTvRes)                 // 应用名称
        iconIv.setImageResource(iconIvRes)              // 图标

        bgBiv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(bgBiv)
            }
        }

        markView.setDebounceTouchClickListener {
            bootAppByAction(bootAction)
        }

        initSubViews()
    }

    open fun FragmentMainQuickBootBinding.initSubViews() {
        // 子类可以重写此方法来初始化其他视图
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropManager.BackdropEvent.CHANGE) {
                binding.bgBiv.post {
                    launch {
                        BackdropManager.setBlurBitmapToBlurIv(binding.bgBiv)
                    }
                }
            }
        }
        repeatCollectOnStart(keyCodeVM.scrollTimeFlow) {
            binding.bgBiv.invalidate()
        }
    }

    override fun generateViewBinding(rootView: ViewGroup?): FragmentMainQuickBootBinding {
        return FragmentMainQuickBootBinding.inflate(layoutInflater, rootView, false)
    }
}

/**
 * StarryPad页面
 */
class StarryPadQuickBootFragment : AbsQuickBootFragment() {
    override val markViewBgRes: Int
        get() = R.drawable.mark_quick_starrypad
    override val iconIvRes: Int
        get() = R.drawable.icon_quick_starrypad
    override val appNameTvRes: Int
        get() = R.string.quick_boot_write_pad
    override val bootAction: String
        get() = BootParam.ACTION_BOOT_WRITE_PAD

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        quickBootViewModel.starryPadNewLive.observe(this) {
            binding.quickBootBadgePoint.gone(!it)
        }
    }
}

class FileQuickBootFragment : AbsQuickBootFragment() {
    override val markViewBgRes: Int
        get() = R.drawable.mark_quick_file
    override val iconIvRes: Int
        get() = R.drawable.icon_quick_file
    override val appNameTvRes: Int
        get() = R.string.quick_boot_file
    override val bootAction: String
        get() = BootParam.ACTION_BOOT_FILE

    private val transCodeBinding: InflateQuickBootFileBinding by lazy {
        InflateQuickBootFileBinding.inflate(layoutInflater, binding.customView)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // 传输校验码
        quickBootViewModel.fileCodeLiveData.observe(this) { fileCode ->
            if (fileCode.isNotEmpty()) {
                transCodeBinding.root.show()
                transCodeBinding.transCodeView.text = fileCode
            } else {
                transCodeBinding.root.gone()
            }
        }

        quickBootViewModel.fileNameNewLive.observe(this) {
            binding.quickBootBadgePoint.gone(!it)
        }
    }
}

class SettingsQuickBootFragment : AbsQuickBootFragment() {
    override val markViewBgRes: Int
        get() = R.drawable.mark_quick_settings
    override val iconIvRes: Int
        get() = R.drawable.icon_quick_settings
    override val appNameTvRes: Int
        get() = R.string.quick_boot_options
    override val bootAction: String
        get() = BootParam.ACTION_BOOT_NOTICE_SETTING

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        quickBootViewModel.settingNewLive.observe(this) {
            binding.quickBootBadgePoint.gone(!it)
        }
    }
}