package com.czur.starry.device.launcher.pages.view.main.quickboot

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileName
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.starrypadlib.StarryPadPaintHandler

/**
 * Created by 陈丰尧 on 2025/7/3
 */
private const val TAG = "QuickBootViewModel"

class QuickBootViewModel(application: Application) : AndroidViewModel(application) {
    val fileCodeLiveData = MediatorLiveData<String>().apply {
        fun updateFileCode() {
            value = if (FileHandlerLive.fileShareCodeEnable && FileHandlerLive.fileShareEnable) {
                FileHandlerLive.fileShareCodeStatus
            } else {
                ""
            }

        }
        addSource(FileHandlerLive.fileShareCodeLive) {
            updateFileCode()
        }
        addSource(FileHandlerLive.fileShareCodeEnableLive) {
            updateFileCode()
        }
        addSource(FileHandlerLive.fileShareEnableLive) {
            updateFileCode()
        }
    }

    val starryPadNewLive = MediatorLiveData<String>().apply {

        fun updateFileName(name: String) {
            logTagV(TAG, "StarryPad新文件名字 = $name")
            value = name
        }

        addSource(StarryPadPaintHandler.newPaintFileNameLive) {
            updateFileName(it)
        }

    }.map { it.isNotEmpty() }

    val fileNameNewLive = MediatorLiveData<String>().apply {

        fun updateFileName() {
            logTagV(TAG, "新文件名字 = $unReadFileName")
            value = unReadFileName
        }

        addSource(FileHandlerLive.unReadFileNameLive) {
            updateFileName()
        }

    }.map { newName ->
        newName != "null" && newName.isNotEmpty()
    }

    val settingNewLive = MediatorLiveData<Boolean>().apply {
        fun updateVersion() {
            val ota = OTAHandler.newVersionStatus
            val touchPad = OTAHandler.newTouchPadVersion
            val camera =
                OTAHandler.newCameraVersion != "null" && OTAHandler.newCameraVersion != OTAHandler.currentCameraVersion
            logTagV(TAG, "ota新版本:$ota , 触控板新版本:${touchPad}")
            value = ota || touchPad || camera
        }

        addSource(OTAHandler.newTouchPadVersionLive) {
            updateVersion()
        }
        addSource(OTAHandler.newVersionStatusLive) {
            updateVersion()
        }
        addSource(OTAHandler.newCameraVersionStatusLive) {
            updateVersion()
        }

    }
}