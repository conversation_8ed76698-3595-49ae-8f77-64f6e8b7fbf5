package com.czur.starry.device.launcher.pages.view.main.screenshare

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil.NetStatus.*
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.launcher.databinding.FragmentMainScreenShareInfoBinding
import com.czur.starry.device.launcher.pages.view.main.ShareViewModel
import com.czur.starry.device.launcher.pages.view.main.AbsMainBlurItemFragment
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.widget.BlurImageView

/**
 * Created by 陈丰尧 on 2025/7/2
 * 无线投屏模块
 */
class ScreenShareInfoFragment : AbsMainBlurItemFragment<FragmentMainScreenShareInfoBinding>() {
    private val shareViewModel: ShareViewModel by activityViewModels()
    override val blurIv: BlurImageView
        get() = binding.screenShareBackgroundBiv

    override fun FragmentMainScreenShareInfoBinding.initSubViews() {
        markView.setDebounceTouchClickListener {
            bootAppByAction(BootParam.ACTION_BOOT_SCREEN_SHARE){
                addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatOnResume {
            shareViewModel.loadShareInfo()
        }
        // 无线投屏名称
        repeatCollectOnResume(shareViewModel.showShareNameFlow) {
            binding.shareNameTv.text = it
        }

        // 网络信息 根据Redmine 24704,这里的显示逻辑是只要连接了wifi, 就显示wifi的信息
        repeatCollectOnResume(shareViewModel.netDisplayFlow) { (name, status) ->
            if (status == NO_NET_WORK){
                binding.wifiInfoGroup.gone()
                binding.ethernetTv.gone()
                return@repeatCollectOnResume
            }
            binding.wifiNameTv.text = name
            if (name.isNotEmpty()){
                binding.wifiInfoGroup.show()
                binding.ethernetTv.gone()
            } else {
                binding.wifiInfoGroup.gone()
                binding.ethernetTv.show()
            }
        }
    }
}