package com.czur.starry.device.launcher.pages.view.main.special

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_TRANSCRIPTION
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.hdmilib.HDMIIFStatus
import com.czur.starry.device.hdmilib.sendBroadCastToMirror
import com.czur.starry.device.launcher.databinding.FragmentMainSpecialFeatureBinding
import com.czur.starry.device.launcher.pages.view.main.ShareViewModel
import com.czur.starry.device.launcher.pages.view.main.HDMIViewModel
import com.czur.starry.device.launcher.pages.view.main.AbsMainBlurItemFragment
import com.czur.starry.device.launcher.utils.addIconHoverAnim
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.widget.BlurImageView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2025/7/3
 * 特色功能页面
 */
private const val TAG = "SpecialFeatureFragment"

class SpecialFeatureFragment : AbsMainBlurItemFragment<FragmentMainSpecialFeatureBinding>() {
    override val blurIv: BlurImageView
        get() = binding.bgBiv

    // hdmi的状态
    private val hdmiVM: HDMIViewModel by activityViewModels()
    private val shareViewModel: ShareViewModel by activityViewModels()


    override fun FragmentMainSpecialFeatureBinding.initSubViews() {
        aiTransGroup.setDebounceTouchClickListener {
            logTagI(TAG, "点击AI互译按钮")
            if (!TransHandler.isTranslating) {
                // 1.1 如果没有启动AI字幕功能, 则正常启动页面(现有逻辑)
                logTagI(TAG, "AI字幕未启动，正常启动页面")
                bootAppByAction(ACTION_BOOT_TRANSCRIPTION)
            } else {
                // AI字幕已经启动
                if (!TransHandler.showSubtitles) {
                    // 1.2 如果AI字幕已经启动, 悬浮窗未显示,则让悬浮窗显示出来
                    logTagI(TAG, "AI字幕已启动但悬浮窗未显示，显示悬浮窗")
                    TransHandler.showSubtitles = true
                } else {
                    // 1.3 如果AI字幕已经启动, 悬浮窗已经显示, 则悬浮窗增加一个抖动效果
                    logTagI(TAG, "AI字幕已启动且悬浮窗已显示，触发抖动效果")
                    TransHandler.triggerFloatingWindowShake()
                }
            }
        }

        aiTransGroup.addIconHoverAnim()

        hdmiGroup.setDebounceTouchClickListener {
            logTagV(TAG, "点击HDMI")
            launch {
                bootHdmiActivity()
            }
        }
        hdmiGroup.addIconHoverAnim()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // HDMI 插入状态
        repeatCollectOnStart(hdmiVM.hdmiStatusFlow) { status ->
            binding.hdmiInMarkIv.gone(status != HDMIIFStatus.IN)
        }
    }

    /**
     * 启动hdmi
     */
    private suspend fun bootHdmiActivity() = withContext(Dispatchers.Main) {
        logTagD(TAG, "bootHdmiActivity")
        val hdmiAirplayOpen = async(Dispatchers.IO) {
            getBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, true)
        }
        val eShareActive = async(Dispatchers.Default) {
            shareViewModel.isEShareActive()
        }
        if (hdmiAirplayOpen.await().also {
                logTagV(TAG, "hdmiAirplayOpen:$it")
            } && eShareActive.await().also {
                logTagV(TAG, "eShareActive:$it")
            }) {
            logTagD(TAG, "HDMI混投开启, EShare激活, 启动EShare HDMI应用")
            sendBroadCastToMirror(requireContext(), true)
        } else {
            logTagW(TAG, "HDMI混投未开启, 或者EShare未激活, 启动原生HDMI应用")
            bootAppByAction(BootParam.ACTION_BOOT_HDMI)
        }
    }
}