package com.czur.starry.device.launcher.pages.view.main.toolsheet

import android.graphics.Bitmap
import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.addItemDecoration
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.launcher.databinding.FloatToolSheetBinding
import com.czur.starry.device.launcher.pages.view.dialog.BootGooglePlayHintFloating
import com.czur.starry.device.launcher.pages.view.dialog.showForceStopDialog
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.pages.view.main.ShareViewModel
import com.czur.starry.device.launcher.utils.BootCheckResult
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.utils.checkBeforeBootApp
import com.czur.starry.device.launcher.utils.saveNeverRemindGooglePlayHint

/**
 * Created by 陈丰尧 on 2023/8/15
 * Feature: 23121增加
 */
class ToolSheetFloat(private val bgImg: Bitmap?) : CZVBFloatingFragment<FloatToolSheetBinding>() {

    companion object {
        private const val TAG = "ToolSheetFloat"
    }

    private val toolSheetAdapter = ToolSheetAdapter()

    // 应用信息的ViewModel
    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })

    private val shareViewModel: ShareViewModel by viewModels({ requireActivity() })

    override fun FloatingFragmentParams.initFloatingParams() {
        if (bgImg != null) {
            floatingBgMode = FloatingBgMode.Image(bgImg)
        } else {
            logTagW(TAG, "ToolSheetFloat: bgImg is null")
            floatingBgMode = FloatingBgMode.Dark
        }
    }

    override fun FloatToolSheetBinding.initBindingViews() {
        // 关闭对话框
        closeBtn.setOnDebounceClickListener {
            dismiss()
        }

        toolSheetAppRv.apply {
            layoutManager = GridLayoutManager(requireContext(), 6)   // 一行6个
            closeDefChangeAnimations()  // 关闭默认刷新动画
            adapter = toolSheetAdapter   // 设置Adapter
            doOnItemClick { vh, _ ->
                val appInfo = toolSheetAdapter.getData(vh.bindingAdapterPosition)
                bootFavApp(appInfo)
                dismiss()
                true
            }
            addItemDecoration(RecyclerView.VERTICAL, 50)
        }
    }

    /**
     * 启动推荐App
     */
    private fun bootFavApp(appInfo: AppInfo) {
        logTagD(TAG, "启动app:${appInfo.appName}")

        if (appInfo.pkgName.isEmpty()) {
            logTagD(TAG, "点击加号, 启动Setting")
            bootAppByAction(BootParam.ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "launcherFavApps")
            }
            return
        }

        when (val checkBeforeBootApp = checkBeforeBootApp(appInfo)) {
            BootCheckResult.ConflictWithMeeting -> {
                toast(R.string.toast_conflict_with_meeting, appInfo.appName)
            }

            is BootCheckResult.ConflictWithMic -> {
                logTagI(TAG, "启动app:${appInfo.appName} 与麦克风冲突")
                showForceStopDialog(
                    requireActivity(),
                    appInfo,
                    checkBeforeBootApp.useMicProcessList.toMutableList()
                ) {
                    bootFavApp(appInfo)
                }
            }

            BootCheckResult.HintGooglePlay -> {
                BootGooglePlayHintFloating { neverRemind, floating ->
                    floating.dismiss()
                    if (neverRemind) {
                        launch {
                            saveNeverRemindGooglePlayHint()
                        }
                    }
                    bootApp(pkgName = appInfo.pkgName, context = requireContext())
                }.show()
            }

            BootCheckResult.Success -> {
                bootApp(pkgName = appInfo.pkgName, context = requireContext())
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        repeatCollectOnResume(appsVM.favAppInfoListFlow) {
            toolSheetAdapter.setData(it)
        }
    }
}