package com.czur.starry.device.launcher.pages.view.main.toolsheet

import android.graphics.Bitmap
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.viewModels
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.launcher.databinding.FragmentMainToolSheetBinding
import com.czur.starry.device.launcher.pages.view.dialog.BootGooglePlayHintFloating
import com.czur.starry.device.launcher.pages.view.dialog.showForceStopDialog
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.pages.view.main.AbsMainBlurItemFragment
import com.czur.starry.device.launcher.utils.BootCheckResult
import com.czur.starry.device.launcher.utils.addIconHoverAnim
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.utils.checkBeforeBootApp
import com.czur.starry.device.launcher.utils.saveNeverRemindGooglePlayHint
import com.czur.starry.device.launcher.widget.BlurImageView

/**
 * Created by 陈丰尧 on 2025/7/3
 */
private const val TAG = "ToolSheetFragment"
private const val ANIM_DURATION_CHANGE_ICON = 300L

class ToolSheetFragment : AbsMainBlurItemFragment<FragmentMainToolSheetBinding>() {
    override val blurIv: BlurImageView
        get() = binding.toolSheetBiv

    private val toolSheetList by lazy {
        listOf(
            SheetToolAppView(R.id.toolSheetAppIv1, R.id.toolSheetAppTv1),
            SheetToolAppView(R.id.toolSheetAppIv2, R.id.toolSheetAppTv2),
            SheetToolAppView(R.id.toolSheetAppIv3, R.id.toolSheetAppTv3),
            SheetToolAppView(R.id.toolSheetAppIv4, R.id.toolSheetAppTv4),
        )
    }

    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })


    override fun FragmentMainToolSheetBinding.initSubViews() {
        toolSheetLayout.setDebounceTouchClickListener {
            launch {
                val bgImg = takeScreenShot(Bitmap.Config.ARGB_8888)?.blur(samplingRate = 4)
                ToolSheetFloat(bgImg).show()
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatOnResume {
            // 每次回到这个页面都重新加载一次
            appsVM.loadFavAppPkgList()
        }

        repeatCollectOnResume(appsVM.favAppInfoListFlow) {
            toolSheetList.forEachIndexed { index, sheetToolAppView ->
                var appInfo = it.getOrNull(index)
                if (appInfo?.pkgName?.isEmpty() == true){
                    appInfo = null  // 不显示最后添加的加号
                }
                sheetToolAppView.appInfo = appInfo
            }
        }
    }


    private inner class SheetToolAppView(val icon: ImageView, val name: TextView) {
        var appInfo: AppInfo? = null
            set(value) {
                if (field != value) {
                    field = value
                    if (value != null) {
                        onAppInfoSet(value)
                    } else {
                        name.clearContentText()
                        icon.setImageBitmap(null)
                        icon.setImageDrawable(null)
                    }
                    icon.invisible(value == null)
                    name.invisible(value == null)
                }
            }

        private val crossFadeBuilder =
            DrawableCrossFadeFactory.Builder(ANIM_DURATION_CHANGE_ICON.toInt())
                .setCrossFadeEnabled(true)

        constructor(iconId: Int, nameId: Int) : this(
            requireView().findViewById<ImageView>(iconId),
            requireView().findViewById(nameId)
        )

        /**
         * 切换图标时的动画效果
         */
        private fun changeIconDrawable(appInfo: AppInfo) {
            // 如果不使用新的Bitmap, 那么动画效果就不会生效
            val bitmap =
                appInfo.appIcon.toBitmap(icon.width, icon.height)
            val builder = Glide.with(icon)
                .load(bitmap)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
            if (icon.drawable == null) {
                builder.into(icon)  // 不加载动画,直接显示
            } else {
                builder
                    .placeholder(icon.drawable)
                    .transition(DrawableTransitionOptions.withCrossFade(crossFadeBuilder))
                    .into(icon)
            }
        }

        private fun onAppInfoSet(appInfo: AppInfo) {
            changeIconDrawable(appInfo)
            name.text = appInfo.appName

            icon.setDebounceTouchClickListener {
                bootFavApp(appInfo)
            }
            name.setDebounceTouchClickListener {
                bootFavApp(appInfo)
            }

            icon.addIconHoverAnim()
        }
    }


    /**
     * 启动推荐App
     */
    private fun bootFavApp(appInfo: AppInfo) {
        logTagD(TAG, "启动app:${appInfo.appName}")

        when (val checkBeforeBootApp = checkBeforeBootApp(appInfo)) {
            BootCheckResult.ConflictWithMeeting -> {
                toast(R.string.toast_conflict_with_meeting, appInfo.appName)
            }

            is BootCheckResult.ConflictWithMic -> {
                logTagI(TAG, "启动app:${appInfo.appName} 与麦克风冲突")
                showForceStopDialog(
                    requireActivity(),
                    appInfo,
                    checkBeforeBootApp.useMicProcessList.toMutableList()
                ) {
                    bootFavApp(appInfo)
                }
            }

            BootCheckResult.HintGooglePlay -> {
                BootGooglePlayHintFloating { neverRemind, floating ->
                    floating.dismiss()
                    if (neverRemind) {
                        launch {
                            saveNeverRemindGooglePlayHint()
                        }
                    }
                    bootApp(pkgName = appInfo.pkgName, context = requireContext())
                }.show()
            }

            BootCheckResult.Success -> {
                bootApp(pkgName = appInfo.pkgName, context = requireContext())
            }

        }
    }
}