package com.czur.starry.device.launcher.recent.adapter

import android.annotation.SuppressLint
import android.graphics.BitmapFactory
import android.graphics.Color
import android.view.ViewGroup
import android.widget.ImageView
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.recent.been.RecentTask
import com.czur.starry.device.launcher.recent.view.RecentActivity.Companion.RECENT_TASK_THUMBNAIL_PATH

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/05/24
 */

private const val TAG = "RecentRecycleAdapter"

class RecentRecycleAdapter : BaseDifferAdapter<RecentTask>() {

    override fun areItemsTheSame(oldItem: RecentTask, newItem: RecentTask): Boolean {
        return oldItem.taskId == newItem.taskId
    }


    @SuppressLint("SuspiciousIndentation")
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: RecentTask) {
        holder.setText(itemData.name, R.id.appNameTv)

        val bitmap = if (position == 0 && itemData.thumbnail == null) {
            BitmapFactory.decodeFile(RECENT_TASK_THUMBNAIL_PATH)
        } else {
            itemData.thumbnail
        }
        holder.visible(bitmap != null, R.id.blackBgView)
        if (bitmap != null) {
            holder.setImgBitmap(bitmap, R.id.recentScreenshotIv)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_recent, parent)
    }
}