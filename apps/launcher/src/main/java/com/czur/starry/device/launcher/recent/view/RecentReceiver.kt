package com.czur.starry.device.launcher.recent.view

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.view.PointerIcon
import android.view.inputmethod.InputMethodManager
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.launcher.recent.view.RecentActivity.Companion.RECENT_FILE_PATH
import com.czur.starry.device.launcher.recent.view.RecentActivity.Companion.RECENT_TASK_THUMBNAIL_PATH
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File


/**
 *  author : <PERSON><PERSON>ao
 *  time   :2024/07/15
 *  处理多任务第一个任务没有截图问题
 */

private const val TAG = "RecentReceiver"

class RecentReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action
        logTagD(TAG, "=?.action ==${action}")
        MainScope().launch(Dispatchers.IO) {
            if (topClassIsLauncher(context)) {
                startRecentActivity(context)
            } else {
                hideMousePointer()
                delay(100) //等待导航栏消失
                saveBitMapToFile(takeScreenShot())
                startRecentActivity(context)
            }
        }
    }

    private fun startRecentActivity(context: Context?) {
        val recentIntent = Intent(context, RecentActivity::class.java)
        recentIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        recentIntent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        context?.startActivity(recentIntent)
    }

    private fun topClassIsLauncher(context: Context?): Boolean {
        val activityManager = context?.getSystemService(ActivityManager::class.java)
        val tasks = activityManager?.getRunningTasks(1)
        if (!tasks.isNullOrEmpty()) {
            val topPackageName = tasks[0].topActivity?.packageName
            return topPackageName == context.packageName
        }
        return false
    }

    private fun hideMousePointer() {
        try {
            val inputManager =  CZURAtyManager.appContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            val setPointerIconTypeMethod = inputManager.javaClass.getDeclaredMethod("setPointerIconType", Int::class.java)
            setPointerIconTypeMethod.invoke(inputManager, PointerIcon.TYPE_NULL)//隐藏鼠标
        }catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 保存大小缩放到显示item大小
     */
    private suspend fun saveBitMapToFile(bitmap: Bitmap?, filename: String = RECENT_TASK_THUMBNAIL_PATH) {

        val file = File(RECENT_FILE_PATH)
        if (!file.exists()) {
            file.mkdir()
        }

        val scaledBitmap = bitmap?.let { Bitmap.createScaledBitmap(it, 500, 282, true) };
        scaledBitmap?.saveToFile(File(filename))
        scaledBitmap?.recycle()
        bitmap?.recycle()
    }
}