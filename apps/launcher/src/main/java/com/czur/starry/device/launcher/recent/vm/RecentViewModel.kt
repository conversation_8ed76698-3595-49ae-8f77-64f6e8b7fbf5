package com.czur.starry.device.launcher.recent.vm

import android.app.ActivityManager
import android.app.Application
import android.graphics.Bitmap
import android.graphics.Rect
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.recent.been.RecentTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlin.math.max

/**
 *  author : WangHao
 *  time   :2024/05/24
 */

private val IGNORE_PKG_LIST = setOf("com.czur.starry.device.launcher")


class RecentViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "RecentViewModel"
        const val RECENT_PAGE_ROW = 2       // 每页行数
        const val RECENT_PAGE_COLUMN = 3    // 每页列数
        const val RECENT_PAGE_SIZE = RECENT_PAGE_ROW * RECENT_PAGE_COLUMN   // 每页数量
        private const val CLEAR_BAR_HEIGHT = 70 // 清理栏高度
    }

    private val _recentAppsFlow = MutableStateFlow(emptyList<RecentTask>())
    private var loadingFinishFlow = MutableStateFlow(false)
    val recentAppsFlow = _recentAppsFlow.combine(loadingFinishFlow) { recentApps, loadingFinish ->
        if (loadingFinish) {
            recentApps
        } else {
            null
        }
    }.filterNotNull()

    // 总页数
    private val pageCountFlow = recentAppsFlow.map { max(0, it.size - 1) / RECENT_PAGE_SIZE + 1 }
    private val _pageIndexFlow = MutableStateFlow(0) // 当前页数
    private val pageIndexFlow = _pageIndexFlow.asStateFlow()

    val pageInfoFlow = pageIndexFlow.combine(pageCountFlow) { pageIndex, pageCount ->
        pageIndex to pageCount
    }.distinctUntilChanged()

    private val activityManagerWrapper by lazy { ActivityManagerWrapper.getInstance() }


    private val _clearBarBmpFlow = MutableStateFlow<Bitmap?>(null)
    val clearBarBmpFlow = _clearBarBmpFlow.asStateFlow()

    private val appUtil by lazy {
        AppUtil()
    }

    init {
        launch {
            loadWallpaper()
        }
    }

    /**
     * 加载最近任务
     */
    suspend fun loadTasks() = withContext(Dispatchers.IO) {
        val listTaskInfo = activityManagerWrapper.getRecentTasks(50)
        taskToRecentTask(listTaskInfo)
        loadingFinishFlow.value = true
    }

    /**
     * 清除最近任务
     */
    suspend fun clearRecentTask(task: RecentTask? = null) {
        logTagD(TAG, "clearRecentTask taskId=$task")
        if (task != null) {
            if (task.pkgName == "com.czur.starry.device.transcription") {
                TransHandler.stopTrans = true
            }
            activityManagerWrapper.removeTask(task)

        } else {
            activityManagerWrapper.removeAllRecentTasks(_recentAppsFlow.value)
        }
        loadTasks()
    }

    /**
     * 更新当前滑到的索引页
     */
    fun updatePageIndex(pageSelIndex: Int) {
        _pageIndexFlow.value = pageSelIndex
    }

    /**
     * 启动任务
     */
    fun startTask(taskId: Int) {
        logTagD(TAG, "startTask taskId=$taskId")
        activityManagerWrapper.startActivityFromRecent(taskId, null)
    }

    /**
     * 获取当前运行的任务
     */
    fun getRunningTaskId(): Int? {
        return activityManagerWrapper.getRunningTask()?.taskId
    }

    /**
     * 转换自定义RecentTask
     */
    private fun taskToRecentTask(listTaskInfo: List<ActivityManager.RecentTaskInfo>) {
        _recentAppsFlow.value = listTaskInfo.mapNotNull { taskInfo ->
            val packageName = taskInfo.baseIntent.component!!.packageName
            if (packageName in IGNORE_PKG_LIST) {
                // 在最近任务中屏蔽掉
                return@mapNotNull null
            }
            logTagD(TAG, "=====taskId=${taskInfo.taskId}")
            RecentTask(
                taskInfo.taskId, getApplicationName(packageName),
                activityManagerWrapper.getTaskThumbnail(taskInfo.taskId),
                pkgName = packageName
            )
        }
    }

    /**
     * 通过packageManager获取应用名称
     */
    private fun getApplicationName(packageName: String): String {
        val appName = appUtil.getApplicationName(packageName)
        return if (appName.isNullOrEmpty()) {
            "Unknown"
        } else {
            appName
        }
    }

    /**
     * 加载壁纸,处理成模糊背景
     */
    private suspend fun loadWallpaper() {
        withContext(Dispatchers.IO) {
            val bitmap = BackdropManager.getBlurBitmap()
            val croppedBmp = bitmap?.let {
                val croppedBitmap = Bitmap.createBitmap(
                    it,
                    0,
                    getScreenHeight() - CLEAR_BAR_HEIGHT,
                    getScreenWidth(),
                    CLEAR_BAR_HEIGHT
                )
                croppedBitmap
            }

            _clearBarBmpFlow.value = croppedBmp
            logTagD(TAG, "loadWallpaper bitmap=$bitmap")
        }
    }
}