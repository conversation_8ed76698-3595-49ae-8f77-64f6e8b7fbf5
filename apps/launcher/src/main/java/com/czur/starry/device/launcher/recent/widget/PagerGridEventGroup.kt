package com.czur.starry.device.launcher.recent.widget

import android.content.Context
import android.util.AttributeSet
import android.view.InputDevice
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.core.view.forEach
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD

private const val TAG = "PagerGridEventGroup"

class PagerGridEventGroup @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private val viewPager: RecyclerView by lazy {
        forEach {
            if (it is RecyclerView) {
                return@lazy it
            }
        }
        throw IllegalStateException("ViewPagerEventGroup must have a ViewPager2 child")
    }

    private val layoutManager: PagerGridLayoutManager? by lazy {
        viewPager.layoutManager as? PagerGridLayoutManager
    }
    private var downX = 0F
    private var downY = 0F

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            downX = ev.x
            downY = ev.y
        } else if (ev.action == MotionEvent.ACTION_MOVE) {
            if (ev.y == downY && ev.source == InputDevice.SOURCE_TOUCHSCREEN) {
                // 横向滑动
                val dx = ev.x - downX
                if (dx < 0) {
                    // 向上滑动
                    logTagD(TAG, "向前翻页")
                    layoutManager?.let {
                        it.smoothScrollToPagerIndex(it.currentPagerIndex + 1)
                    }
                } else {
                    // 向下滑动
                    logTagD(TAG, "向后翻页")
                    layoutManager?.let {
                        it.smoothScrollToPagerIndex(it.currentPagerIndex - 1)
                    }
                }
                return true
            }
        }
        return super.onInterceptTouchEvent(ev)
    }
}