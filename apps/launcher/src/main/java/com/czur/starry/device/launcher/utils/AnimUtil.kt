package com.czur.starry.device.launcher.utils

import android.graphics.Outline
import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import android.view.ViewOutlineProvider
import com.czur.starry.device.baselib.utils.scaleXY

/**
 * Created by 陈丰尧 on 2025/7/9
 */
private const val ICON_HOVER_ANIM_DURATION = 100L
fun View.addIconHoverAnim(ignoreHover:() -> <PERSON><PERSON><PERSON> = { false }) {
    post {
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(0, 0, view.width, view.height, 20F)
            }
        }
    }
    setOnHoverListener { view, event ->
        if (ignoreHover()) return@setOnHoverListener false
        when (event.action) {
            MotionEvent.ACTION_HOVER_ENTER -> {
                animate()
                    .translationZ(10F)
                    .scaleXY(1.07F)
                    .setDuration(ICON_HOVER_ANIM_DURATION)
                    .start()
            }

            MotionEvent.ACTION_HOVER_EXIT -> {
                animate()
                    .rotationX(0F)
                    .rotationY(0F)
                    .scaleXY(1F)
                    .translationZ(0F)
                    .setDuration(ICON_HOVER_ANIM_DURATION).start()
            }
        }
        false
    }
}