package com.czur.starry.device.launcher.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.SweepGradient
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.*
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import androidx.core.graphics.toColorInt

/**
 * Created by 陈丰尧 on 2025/6/4
 */
class LauncherMainMarkView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val paint = Paint().apply {
        isAntiAlias = true
    }

    private val sweepGradient by lazy {
        SweepGradient(
            width / 2f, height / 2f,
            intArrayOf(
                "#1AFFFFFF".toColorInt(),
                Color.WHITE,
                "#48FFFFFF".toColorInt(),
                "#1AFFFFFF".toColorInt()
            ),
            floatArrayOf(0F, 0.5F, 0.9F, 1F)
        )
    }

    private val contentColor = "#33FFFFFF".toColorInt()

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制圆角矩形
        paint.shader = null
        paint.style = Paint.Style.FILL
        paint.color = contentColor
        canvas.drawRoundRect(1f, 1f, width.toFloat() - 1, height.toFloat() - 1, 40f, 40f, paint)
        // 绘制边框
        paint.strokeWidth = 1.5F
        paint.shader = sweepGradient
        paint.color = Color.WHITE
        paint.style = Paint.Style.STROKE
        canvas.drawRoundRect(1f, 1f, width.toFloat() - 1, height.toFloat() - 1, 40f, 40f, paint)
    }
}