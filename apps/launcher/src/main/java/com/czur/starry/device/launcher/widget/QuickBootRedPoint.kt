package com.czur.starry.device.launcher.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import kotlin.math.max

/**
 * Created by 陈丰尧 on 2025/6/6
 */
class QuickBootRedPoint @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val paint = Paint()
    private val showShadow = true

    var shadowColor: Int? = null
        set(value) {
            field = value
            invalidate() // 当阴影颜色变化时重绘
        }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制白色边框 红色填充 圆形
        val width = width.toFloat()
        val height = height.toFloat()
        val radius = max(width, height) / 2
        paint.color = Color.RED
        paint.style = Paint.Style.FILL
        paint.isAntiAlias = true
        if (showShadow) {
            shadowColor?.let {
                paint.setShadowLayer(8f, 0f, 2f, it)
            } ?: run {
                paint.setShadowLayer(8f, 0f, 2f, Color.BLACK)
            }
            repeat(1) { // 重复次数越高, 阴影越重
                canvas.drawCircle(width / 2, height / 2, radius, paint)
            }
        } else {
            canvas.drawCircle(width / 2, height / 2, radius, paint)
        }
        paint.color = Color.WHITE
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 2F
        paint.clearShadowLayer()
        canvas.drawCircle(width / 2, height / 2, radius, paint)
    }


}