package com.czur.starry.device.launcher.widget

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Shader
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.animation.LinearInterpolator
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.core.graphics.withTranslation
import com.czur.starry.device.baselib.utils.scaleXY
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.utils.addIconHoverAnim
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2022/3/31
 * App抖动
 */
@SuppressLint("ClickableViewAccessibility")
class ShakeIV @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : AppCompatImageView(context, attrs, defStyleAttr) {
    companion object {
        private const val MAX_OFF_SET_ANGLE_MIN = 1  // 每次抖动的最大偏移量
        private const val MAX_OFF_SET_ANGLE_MAX = 5  // 每次抖动的最大偏移量
        private const val MAX_OFF_SET_X = 4
        private const val MAX_OFF_SET_Y = 3
        private const val SHAKE_CYCLE_MAX = 15  // 抖动的最大次数
        private const val SHAKE_CYCLE_MIN = 12  // 抖动的最小次数

        private const val DURATION_MIN = 2500L
        private const val DURATION_MAX = 3000L
    }

    private val shakeAnim by lazy {
        initAnim()
    }

    private val paint = Paint().apply {
        isAntiAlias = true
        isDither = true
    }

    private val drawMatrix = Matrix()

    // 缓存相关变量
    private var cachedShader: BitmapShader? = null
    private var cachedBitmap: android.graphics.Bitmap? = null
    private var cachedViewWidth = 0
    private var cachedViewHeight = 0

    private var upTime = 0L

    private fun initAnim(): AnimatorSet {
        val shakeCount = Random.nextInt(SHAKE_CYCLE_MIN, SHAKE_CYCLE_MAX)
        val arr = FloatArray(shakeCount)
        val arrX = FloatArray(shakeCount)
        val yCount = Random.nextInt(SHAKE_CYCLE_MIN, SHAKE_CYCLE_MAX)
        val arrY = FloatArray(yCount)
        repeat(yCount) {
            val y = Random.nextInt(1, MAX_OFF_SET_Y).toFloat()
            if (it % 2 == 0) {
                arrY[it] = y
            } else {
                arrY[it] = -y
            }
        }
        // 让抖动的初始方向也不一样
        val direction = if (Random.nextBoolean()) 1 else -1
        repeat(shakeCount) {
            val angle = Random.nextInt(MAX_OFF_SET_ANGLE_MIN, MAX_OFF_SET_ANGLE_MAX).toFloat()
            val x = Random.nextInt(1, MAX_OFF_SET_X).toFloat()
            if (it % 2 == 0) {
                arr[it] = angle * direction
                arrX[it] = x * direction
            } else {
                arr[it] = -angle * direction
                arrX[it] = -x * direction
            }
        }
        val anim = AnimatorSet()

        val angleAnim = ObjectAnimator.ofFloat(this, "rotation", *arr)

        val angleDuration = Random.nextLong(DURATION_MIN, DURATION_MAX)
        angleAnim.duration = angleDuration
        angleAnim.repeatCount = ValueAnimator.INFINITE
        angleAnim.repeatMode = ValueAnimator.REVERSE
        angleAnim.interpolator = LinearInterpolator()

        val xAnim = ObjectAnimator.ofFloat(this, "translationX", *arrX)
        xAnim.duration = angleDuration
        xAnim.repeatCount = ValueAnimator.INFINITE
        xAnim.repeatMode = ValueAnimator.REVERSE
        xAnim.interpolator = LinearInterpolator()

        val yAnim = ObjectAnimator.ofFloat(this, "translationY", *arrY)
        yAnim.duration = Random.nextLong(DURATION_MIN, DURATION_MAX)
        yAnim.repeatCount = ValueAnimator.INFINITE
        yAnim.repeatMode = ValueAnimator.REVERSE
        yAnim.interpolator = LinearInterpolator()

        anim.playTogether(angleAnim, xAnim, yAnim)

        return anim
    }


    init {
        addIconHoverAnim {
            // 如果正在卸载, 则不响应hover事件
            shakeAnim.isRunning || System.currentTimeMillis() - upTime < 20
        }

        setOnTouchListener { v, event ->
            if (shakeAnim.isRunning) return@setOnTouchListener false  // 卸载模式不响应
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    v.animate().scaleXY(0.9F).setDuration(150).start()
                    setColorFilter(ContextCompat.getColor(context, R.color.filter_app_click))
                    return@setOnTouchListener true
                }

                MotionEvent.ACTION_UP,
                MotionEvent.ACTION_CANCEL -> {
                    upTime = System.currentTimeMillis()
                    v.animate().scaleXY(1F).setDuration(150).start()
                    colorFilter = null
                }
            }
            false
        }
    }

    /**
     * 开始抖动
     */
    fun startShake() {
        shakeAnim.start()
    }

    /**
     * 停止抖动
     */
    fun stopShake() {
        shakeAnim.cancel()
        rotation = 0F
        translationX = 0F
        translationY = 0F
    }

    /**
     * 获取或创建 BitmapShader，使用缓存优化性能
     */
    private fun getOrCreateShader(bitmap: android.graphics.Bitmap): BitmapShader {
        // 检查是否需要重新创建 Shader
        val needRecreate = cachedShader == null ||
                          cachedBitmap != bitmap ||
                          cachedViewWidth != width ||
                          cachedViewHeight != height

        if (needRecreate) {
            // 创建新的 BitmapShader
            cachedShader = BitmapShader(
                bitmap,
                Shader.TileMode.DECAL,
                Shader.TileMode.DECAL
            )

            // 更新缓存信息
            cachedBitmap = bitmap
            cachedViewWidth = width
            cachedViewHeight = height
        }

        return cachedShader!!
    }

    /**
     * 清理 Shader 缓存
     */
    private fun clearShaderCache() {
        cachedShader = null
        cachedBitmap = null
        cachedViewWidth = 0
        cachedViewHeight = 0
    }

    override fun setImageDrawable(drawable: android.graphics.drawable.Drawable?) {
        clearShaderCache()
        super.setImageDrawable(drawable)
    }

    override fun setImageBitmap(bm: android.graphics.Bitmap?) {
        clearShaderCache()
        super.setImageBitmap(bm)
    }

    override fun setImageResource(resId: Int) {
        clearShaderCache()
        super.setImageResource(resId)
    }

    /**
     * 修复了旋转动画的时候,图标边缘的锯齿问题:
     * https://blog.csdn.net/DengDongQi/article/details/100524846
     */
    override fun onDraw(canvas: Canvas) {
        val drawable = getDrawable()
        if (drawable is BitmapDrawable) {
            val bitmap = drawable.bitmap

            // 定义留白大小（四周各2像素）
            val padding = 4f
            val paddingDouble = padding * 2

            // 计算可绘制区域的尺寸（减去四周留白）
            val drawableWidth = width.toFloat() - paddingDouble
            val drawableHeight = height.toFloat() - paddingDouble

            // 计算 Bitmap 的原始尺寸
            val bitmapWidth = bitmap.width.toFloat()
            val bitmapHeight = bitmap.height.toFloat()

            // 计算缩放比例（center-inside 模式：选择较小的缩放比例以完全显示图像）
            val scaleX = drawableWidth / bitmapWidth
            val scaleY = drawableHeight / bitmapHeight
            val scale = minOf(scaleX, scaleY)

            // 计算缩放后的 Bitmap 尺寸
            val scaledBitmapWidth = bitmapWidth * scale
            val scaledBitmapHeight = bitmapHeight * scale

            // 计算居中偏移量
            val offsetX = (drawableWidth - scaledBitmapWidth) * 0.5f
            val offsetY = (drawableHeight - scaledBitmapHeight) * 0.5f

            // 设置 Matrix 变换：先缩放，再平移到居中位置
            drawMatrix.reset()
            drawMatrix.setScale(scale, scale)
            drawMatrix.postTranslate(offsetX, offsetY)

            // 获取或创建 BitmapShader（使用缓存优化性能）
            val shader = getOrCreateShader(bitmap)
            shader.setLocalMatrix(drawMatrix)

            // 设置画笔
            paint.shader = shader

            // 保存 Canvas 状态
            canvas.withTranslation(padding, padding) {

                // 平移到留白位置开始绘制
                // 只绘制实际的缩放后 Bitmap 区域（避免裁切）
                drawRect(
                    offsetX, offsetY,
                    offsetX + scaledBitmapWidth, offsetY + scaledBitmapHeight,
                    paint
                )
            }
        }
    }

  
}