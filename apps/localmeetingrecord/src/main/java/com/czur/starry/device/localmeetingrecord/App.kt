package com.czur.starry.device.localmeetingrecord

import com.czer.starry.device.meetlib.MeetingHandler
import com.czur.starry.device.baselib.base.listener.StarryApp
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 2022/8/18
 */
class App : StarryApp(), Thread.UncaughtExceptionHandler {
    companion object {
        var instance: App by Delegates.notNull()
    }

    private var defaultHandler: Thread.UncaughtExceptionHandler? = null
    // 需要跨Aty使用, 表示全部退出的信号
    val finishAppFlow = MutableSharedFlow<Long>()

    override fun onCreate() {
        super.onCreate()

        instance = this
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler(this)
    }

    override fun uncaughtException(t: Thread, e: Throwable) {
        MeetingHandler.localMeetingRecording = true
        MeetingHandler.localMeetingVideoRecording = true
        defaultHandler?.uncaughtException(t, e)
    }
}