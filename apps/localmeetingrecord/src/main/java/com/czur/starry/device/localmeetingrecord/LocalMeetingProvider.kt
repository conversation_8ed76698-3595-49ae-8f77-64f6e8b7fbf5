package com.czur.starry.device.localmeetingrecord

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czer.starry.device.meetlib.MeetingHandler
import com.czer.starry.device.meetlib.MeetingHandler.KEY_LOCAL_MEETING_RECORDING
import com.czer.starry.device.meetlib.MeetingHandler.KEY_LOCAL_MEETING_VIDEO_RECORDING
import com.czur.starry.device.baselib.common.KEY_MEETING_REJECT_LOCAL_RECORD
import com.czur.starry.device.baselib.common.KEY_MEETING_REJECT_LOCAL_VIDEO_RECORD
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.handler.SPContentProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp

/**
 * Created by 陈丰尧 on 2025/2/25
 */
class LocalMeetingProvider : SPContentProvider(){
    companion object {
        private const val TAG = "MeetingProvider"
    }

    override val Context.providerDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "LocalMeetingProvider",
        corruptionHandler = createDefCorruptionHandler("LocalMeetingProvider")
    )
    override val targetHandler: SPContentHandler by lazy { MeetingHandler }


    override fun onSubQuery(key: String, defValue: String): String? {
        return when (key) {
            KEY_LOCAL_MEETING_RECORDING -> getLocalMeetingRecording().toString()
            KEY_LOCAL_MEETING_VIDEO_RECORDING -> getLocalMeetingVideoRecording().toString()
            else -> null
        }
    }


    private fun getLocalMeetingRecording(): Boolean {
        return getBooleanSystemProp(KEY_MEETING_REJECT_LOCAL_RECORD, false)
    }

    private fun setLocalMeetingRecording(recording: Boolean) {
        setBooleanSystemProp(KEY_MEETING_REJECT_LOCAL_RECORD, recording)
    }

    private fun getLocalMeetingVideoRecording(): Boolean {
        return getBooleanSystemProp(KEY_MEETING_REJECT_LOCAL_VIDEO_RECORD, false)
    }

    private fun setLocalMeetingVideoRecording(recording: Boolean) {
        setBooleanSystemProp(KEY_MEETING_REJECT_LOCAL_VIDEO_RECORD, recording)
    }

    override fun onSubUpdate(key: String, value: String): Boolean {
        if (key == KEY_LOCAL_MEETING_RECORDING) {
            setLocalMeetingRecording(value.toBoolean())
        }

        if (key == KEY_LOCAL_MEETING_VIDEO_RECORDING) {
            setLocalMeetingVideoRecording(value.toBoolean())
        }

        return false
    }
}