package com.czur.starry.device.localmeetingrecord.jni

import android.graphics.Bitmap
import android.graphics.ImageFormat
import android.graphics.Rect
import android.graphics.YuvImage
import java.io.ByteArrayOutputStream

object YuvOsdUtils {
    init {
        System.loadLibrary("YuvOsdUtils")
    }

    /**
     * 初始化时间水印
     *
     * @param osdOffX     水印在视频左上的x偏移
     * @param osdOffY     水印在视频左上的y 偏移
     * @param patternLen  水印格式长度
     * @param frameWidth  相机宽
     * @param frameHeight 相机高
     * @param rotation    旋转角度,0,90,180,270
     */
    external fun initOsd(
        osdOffX: Int,
        osdOffY: Int,
        patternLen: Int,
        frameWidth: Int,
        frameHeight: Int,
        rotation: Int
    )

    /**
     * 释放内存
     */
    external fun releaseOsd()
    external fun addOsd(yuvInData: ByteArray?, outYvuData: ByteArray?, date: String?)

    /**
     * nv12 与nv21区别
     * NV12: YYYYYYYY UVUV     =>YUV420SP
     * NV21: YYYYYYYY VUVU     =>YUV420SP
     * rgb 转 nv21
     *
     * @param argb
     * @param width
     * @param height
     * @return
     */
    external fun argbIntToNV21Byte(argb: IntArray?, width: Int, height: Int): ByteArray

    /**
     * rgb 转nv12
     *
     * @param argb
     * @param width
     * @param height
     * @return
     */
    external fun argbIntToNV12Byte(argb: IntArray?, width: Int, height: Int): ByteArray

    /**
     * rgb 转灰度 nv
     * 也就是yuv 中只有 yyyy 没有uv 数据
     *
     * @param argb
     * @param width
     * @param height
     * @return
     */
    external fun argbIntToGrayNVByte(argb: IntArray?, width: Int, height: Int): ByteArray

    /**
     * nv21 转 nv 12
     *
     * @param nv21Src  源数据
     * @param nv12Dest 目标数组
     * @param width    数组长度 len=width*height*3/2
     * @param height
     */
    external fun nv21ToNv12(nv21Src: ByteArray?, nv12Dest: ByteArray?, width: Int, height: Int)

    /**
     * @param bitmap cannot be used after call this function
     * @param width  the width of bitmap
     * @param height the height of bitmap
     * @return return the NV21 byte array, length = width * height * 3 / 2
     */
    fun bitmapToNV21(
        bitmap: Bitmap,
        width: Int,
        height: Int
    ): ByteArray {
        val argb = IntArray(width * height)
        bitmap.getPixels(argb, 0, width, 0, 0, width, height)
        return argbIntToNV21Byte(argb, width, height)
    }

    /**
     * @param bitmap cannot be used after call this function
     * @param width  the width of bitmap
     * @param height the height of bitmap
     * @return return the NV12 byte array, length = width * height * 3 / 2
     */
    fun bitmapToNV12(
        bitmap: Bitmap,
        width: Int,
        height: Int
    ): ByteArray {
        val argb = IntArray(width * height)
        bitmap.getPixels(argb, 0, width, 0, 0, width, height)
        return argbIntToNV12Byte(argb, width, height)
    }

    /**
     * @param bitmap cannot be used after call this function
     * @param width  the width of bitmap
     * @param height the height of bitmap
     * @return return the NV12 byte array, length = width * height
     */
    fun bitmapToGrayNV(
        bitmap: Bitmap,
        width: Int,
        height: Int
    ): ByteArray {
        val argb = IntArray(width * height)
        bitmap.getPixels(argb, 0, width, 0, 0, width, height)
        return argbIntToGrayNVByte(argb, width, height)
    }

    /**
     * java 版，速度比c 慢
     *
     * @param nv21
     * @param width
     * @param height
     */
    fun NV21ToNV12(nv21: ByteArray?, width: Int, height: Int) {
        if (nv21 == null) return
        val framesize = width * height
        var j = 0
        val end = framesize + framesize / 2
        var temp: Byte = 0
        j = framesize
        while (j < end) {
            temp = nv21[j]
            nv21[j] = nv21[j + 1]
            nv21[j + 1] = temp
            j += 2
        }
    }

    @JvmOverloads
    fun cameraFrameToArray(yuv: ByteArray?, w: Int, h: Int, quality: Int = 100): ByteArray {
        val img = YuvImage(
            yuv, ImageFormat.NV21,
            w, h, null
        )
        val rect = Rect(0, 0, w, h)
        val os = ByteArrayOutputStream()
        val tmp = os.use {
            img.compressToJpeg(rect, quality, os)
            os.toByteArray() //裁剪后的人脸图
        }
        return tmp
    }


}