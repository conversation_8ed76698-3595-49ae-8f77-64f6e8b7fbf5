package com.czur.starry.device.localmeetingrecord.monitor

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ImageFormat
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.YuvImage
import android.hardware.Camera
import android.media.AudioRecord
import android.os.Environment
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.CameraType
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.logEnableV
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_CHANNEL_CONFIG
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FORMAT
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_SAMPLE_RATE
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_SOURCE
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_SOURCE_SUBMIX
import com.czur.starry.device.localmeetingrecord.Config.SRC_VIDEO_HEIGHT
import com.czur.starry.device.localmeetingrecord.Config.SRC_VIDEO_WIDTH
import com.czur.starry.device.localmeetingrecord.MainViewModel
import com.czur.starry.device.localmeetingrecord.RecordState
import com.czur.starry.device.localmeetingrecord.mdoel.RecordConfigModel
import com.czur.starry.device.localmeetingrecord.monitor.audio.AudioMixer
import com.czur.starry.device.localmeetingrecord.utils.PreviewHelper
import com.czur.starry.device.localmeetingrecord.widget.CameraView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.IOException

object CameraWrapper {
    private const val TAG = "CameraWrapper"

    private var scope = CoroutineScope(Job())

    private var mCamera: Camera? = null
    private lateinit var mCameraParameters: Camera.Parameters
    var isPreview = false
        private set
    private var mCameraPreviewCallback: CameraPreviewCallback? = null

    //    private var openCameraId = -1
    var startRecordingFlag = false
    var mIsInitSuccess = false

    var recStatus: RecordState? = RecordState.STOP

    private var audioJob: Job? = null
    private val audioBufferSize: Int by lazy {
        AudioRecord.getMinBufferSize(AUDIO_SAMPLE_RATE, AUDIO_CHANNEL_CONFIG, AUDIO_FORMAT)
    }

    var cameraListener: CameraListener? = null

    var takeAPictureListener: TakeAPictureListener? = null


    var takeAPicture = false
    private var saveLastPreview = true // 保存最后一帧图像
    private var onFirstFrameCallback: (() -> Unit)? = null

    var frameCount = 0
    var lastSaveFrameTime = 0L
    var lastAllSaveFrameTime = 0L
    var second = 0
    var allFrameCount = 0

    /**
     * 设置相机宽高
     */
    fun setCameraSize(width: Int, height: Int): CameraWrapper {
        SRC_VIDEO_WIDTH = width
        SRC_VIDEO_HEIGHT = height
        return this
    }

    fun rePreviewCamera(width: Int, height: Int, cameraView: CameraView) {
        try {
            // 1. Stop preview first
            mCamera?.setPreviewCallbackWithBuffer(null)
            mCamera?.setPreviewCallback(null)
            mCamera?.stopPreview()
            isPreview = false

            // 2. Update desired preview size
            SRC_VIDEO_WIDTH = width
            SRC_VIDEO_HEIGHT = height

            mCamera?.let { camera ->
                // 3. Get and update parameters
                val parameters = camera.parameters
                parameters.setPreviewSize(width, height)

                // 4. Set new parameters
                camera.parameters = parameters

                // 5. Set camera display
                cameraView.setCameraDisplay(camera)
                // 6. Calculate new buffer size and add it
                // 8. Start preview
//                camera.startPreview()

                logTagD(TAG, "Preview size updated: width=$width, height=$height")
            }
        } catch (e: Exception) {
            e.message?.let { logTagE(TAG, "Failed to change preview size", it) }
        }
    }

    fun doOpenCamera(cameraView: CameraView): Boolean {
        val openCameraId = getFrontCameraId()   // 只有一个Camera
        mCamera = try {
            logTagI(TAG, "Camera open ... openCameraId = $openCameraId")
            Camera.open(openCameraId)
        } catch (e: Exception) {
            logTagI(TAG, "doOpenCamera: 打开失败 $openCameraId", tr = e)
            return false
        }
        try {
            mCamera?.let {
                cameraView.setCameraDisplay(it)
            }
//            mCamera?.setPreviewDisplay(surfaceView.holder)
        } catch (e: IOException) {
            e.printStackTrace()
        }
        mCamera?.setErrorCallback { error, _ ->
            logTagE(TAG, "onError: $error")
            //停止录制
            cameraListener?.onCameraErrorStatus(error)
        }
        logTagI(TAG, "Camera 打开完成...")
//        startAudioJob()
        return initCamera()
    }

    var audioRecorder: AudioRecord? = null
    var submixAudioRecorder: AudioRecord? = null

    @SuppressLint("MissingPermission")
    fun startAudioJob(
        enableWithAudioMark: Boolean,
        enableWithSubmixAudioMark: Boolean,
    ) {
        logTagI(TAG, "开始录音协程")
        audioJob?.let {
            logTagW(TAG, "startAudioJob: audioJob 已经存在!!")
            it.cancel()
        }
        audioJob = scope.launch {
            if (enableWithAudioMark) {
                audioRecorder = AudioRecord(
                    AUDIO_SOURCE,
                    AUDIO_SAMPLE_RATE,
                    AUDIO_CHANNEL_CONFIG,
                    AUDIO_FORMAT,
                    audioBufferSize
                )

                audioRecorder?.startRecording()
            }

            if (enableWithSubmixAudioMark) {
                submixAudioRecorder = AudioRecord(
                    AUDIO_SOURCE_SUBMIX,
                    AUDIO_SAMPLE_RATE,
                    AUDIO_CHANNEL_CONFIG,
                    AUDIO_FORMAT,
                    audioBufferSize
                )

                submixAudioRecorder?.startRecording()
            }
            val outputArray = ByteArray(audioBufferSize)
            val submixOutputArray = ByteArray(audioBufferSize)
            var submixOutputCopyArray = ByteArray(audioBufferSize)
            // 录制一启动, 就持续采集音频数据, 只不过不编码到文件中
            launch {
                while (enableWithAudioMark && isActive) {
                    val readCode = audioRecorder?.read(outputArray, 0, audioBufferSize)
                    if (readCode!! > 0 && recStatus == RecordState.REC) {
                        val outputCopyArray = outputArray.copyOf(readCode)

                        MuxerManager.instance.addAudioFrameData(
                            outputCopyArray,
                            AudioMixer.AUDIO_SOURCE_MIC
                        )
                    }
                }
                logTagD(TAG, "音频录制结束(audioSource)")
            }

            launch {
                while (enableWithSubmixAudioMark && isActive) {
                    val readCode = submixAudioRecorder?.read(submixOutputArray, 0, audioBufferSize)
                    if (readCode!! > 0 && recStatus == RecordState.REC) {
                        submixOutputCopyArray = submixOutputArray.copyOf(readCode)
                        MuxerManager.instance.addAudioFrameData(
                            submixOutputCopyArray,
                            AudioMixer.AUDIO_SOURCE_SUBMIX
                        )
                    }
                }
                logTagD(TAG, "音频录制结束(submixSource)")
            }
            outputArray.fill(0)
            submixOutputArray.fill(0)
            submixOutputCopyArray.fill(0)
        }
    }

    fun releaseAudio() {
        logTagD(TAG, "releaseAudio")
        audioJob?.cancel()
        audioJob = null
        if (audioRecorder != null && audioRecorder?.state == AudioRecord.STATE_INITIALIZED) {
            audioRecorder?.release()
        }
        if (submixAudioRecorder != null && submixAudioRecorder?.state == AudioRecord.STATE_INITIALIZED) {
            submixAudioRecorder?.release()
        }
    }

    private var flag = false

    /**
     * 开始预览
     */
    fun startPreview(firstFrameReceive: (() -> Unit)? = null) {
        if (!mIsInitSuccess) {
            logTagE(TAG, "camera 初始化失败")
            return
        }
        if (!isPreview) {
//            MediaMuxerRunnable.reStartMuxer();

            /*  String newVideoPath = Utils.getVideoFilePath();
            startRecording(newVideoPath);*/
            mCameraPreviewCallback = CameraPreviewCallback()
            mCamera?.setPreviewCallbackWithBuffer(mCameraPreviewCallback)
            onFirstFrameCallback = firstFrameReceive
            // 添加callback buffer，这对setPreviewCallbackWithBuffer很重要
            val previewSize = mCamera?.parameters?.previewSize
            if (previewSize != null) {
                val bufSize = previewSize.width * previewSize.height * 3 / 2
                // 预分配 6 个缓冲区,作用很小,一般3个就够了
                val buffers = List(6) { ByteArray(bufSize) }
                buffers.forEach { mCamera?.addCallbackBuffer(it) }
                logTagD(
                    TAG,
                    "添加callback buffer, size: $bufSize (${previewSize.width}x${previewSize.height})"
                )
            } else {
                logTagE(TAG, "previewSize is null!")
            }

            logTagD(TAG, "准备启动camera预览...")

            mCamera?.startPreview()
            isPreview = true

            logTagD(TAG, "camera startPreview${mCamera?.parameters?.previewSize?.width},${mCamera?.parameters?.previewSize?.height}")
        } else {
            logTagE(TAG, "camera 已经在预览")
        }
    }

    //仅操作camera,不操作数据
    fun justPausePreview() {
        if (!mIsInitSuccess) {
            logTagE(TAG, "camera 还没有初始化,不能暂停预览")
            return
        }

        if (isPreview) {
//            mCamera.setPreviewCallbackWithBuffer(null);
            mCamera?.stopPreview()
            isPreview = false
            logTagD(TAG, "停止预览")
        } else {
            logTagW(TAG, "Camera没有处于预览状态")
        }
    }

    //仅操作camera,不操作数据
    fun justReleaseCamera() {
        logTagD(TAG, "justReleaseCamera开始")
        unregisterCameraErrorCallBack()
        mCamera?.let {
            it.setPreviewCallbackWithBuffer(null)
            it.setPreviewCallback(null)
            it.stopPreview()
            it.release()
            mCamera = null
        }
        logTagD(TAG, "justReleaseCamera结束")

    }

    /**
     * 释放camera 资源
     */
    fun releaseCamera() {
        unregisterCameraErrorCallBack()
        logTagD(TAG, "releaseCamera开始")

        logTagI(TAG, "doStopCamera")
        if (isPreview) {
            mCamera?.let {
                MuxerManager.instance.stopMediaManager()
                it.setPreviewCallbackWithBuffer(null)
                it.setPreviewCallback(null)
                it.stopPreview()
                isPreview = false
                it.release()
                mCamera = null
                startRecordingFlag = false
            }
        }

        releaseAudio()
        logTagD(TAG, "releaseCamera结束")
    }

    private fun initCamera(): Boolean {
        if (mCamera != null) {
            mCameraParameters = mCamera!!.parameters
            var has4KSize = false
            for (size in mCameraParameters.supportedPreviewSizes) {
                if (size.width == Config.SRC_VIDEO_WIDTH_2160 && size.height == Config.SRC_VIDEO_HEIGHT_2160) {
                    has4KSize = true
                }
                logTagD(TAG, "support preview size={${size.width},${size.height}}")
            }
            if (has4KSize) {
                mCameraParameters.setPictureSize(
                    Config.SRC_VIDEO_WIDTH_2160,
                    Config.SRC_VIDEO_HEIGHT_2160
                )
            } else {
                mCameraParameters.setPictureSize(
                    Config.SRC_VIDEO_WIDTH_1080,
                    Config.SRC_VIDEO_HEIGHT_1080
                )
            }
            mCameraParameters.previewFormat = ImageFormat.NV21
            mCamera!!.setDisplayOrientation(if (Config.IS_PORT) 90 else 0)
            mCameraParameters.setPreviewSize(SRC_VIDEO_WIDTH, SRC_VIDEO_HEIGHT)
            val fpsRange = mCameraParameters.supportedPreviewFpsRange
            if (Constants.starryHWInfo.cameraInfo.type != CameraType.CAMERA_4K) {//不是4k设置15帧  4k的默认30帧
                if (fpsRange != null && fpsRange.size > 0) {
                    var minFps = 0
                    var maxFps = 0
                    for (range in fpsRange) {
                        val newMin = range[Camera.Parameters.PREVIEW_FPS_MIN_INDEX]
                        val newMax = range[Camera.Parameters.PREVIEW_FPS_MAX_INDEX]
                        logTagI(TAG, "支持帧率newMin:${newMin} newMax:${newMax}")
                        if (minFps == 0 || newMin < minFps) minFps = newMin
                        if (newMax > 15 * 1000) maxFps = 15 * 1000 // 我们将上限设置为15
                    }
                    logTagI(TAG, "帧率minFps:${minFps} maxFps:${maxFps} 都设置${maxFps}")

                    mCameraParameters.setPreviewFpsRange(maxFps, maxFps)
                }
            }

//            this.mCameraParamters.set("preview-flip", "off");
            val param = mCameraParameters.flatten()
            val split = param.split(";").toTypedArray()
            for (i in split.indices) {
                logTagD(TAG, split[i])
            }
            mCamera!!.parameters = mCameraParameters
            val parameters = mCamera!!.parameters
            val sz = parameters.previewSize
            if (logEnableV) {
                val range = IntArray(2)
                mCameraParameters.getPreviewFpsRange(range)
                val fps = mCameraParameters.supportedPreviewFpsRange
                logTagI(TAG, "initCamera: fps ${fps.joinToString()}")
            }
            mCameraPreviewCallback = CameraPreviewCallback()
            mIsInitSuccess = true
            return true
        }
        return false
    }

    /**
     * 开始录制
     * create at 2017/3/22 17:10
     */
    fun startRecording(
        filePath: String,
        config: RecordConfigModel
    ) {
        startRecordingFlag = true
        logTagI(TAG, "开始录制: $startRecordingFlag $filePath")
        logTagI(TAG, "录制配置: $config")
        MuxerManager.instance.reStartMuxer(filePath, config)
    }

    /**
     * 暂停录制
     * create at 2017/3/22 17:10
     */
    fun pauseCameraRecording() {
        startRecordingFlag = false
        MuxerManager.instance.pauseMuxer()
    }

    fun resumeCameraRecording() {
        startRecordingFlag = true
        MuxerManager.instance.resumeMuxer()
    }

    /**
     * 结束录制
     */
    fun stopRecording(model: MainViewModel) {
        startRecordingFlag = false
        logTagI(TAG, "stopRecording--start:${startRecordingFlag}")
        MuxerManager.instance.stopMediaManager()
        logTagI(TAG, "stopRecording--middle:${startRecordingFlag}")
        model.launch(Dispatchers.IO) {
            releaseAudio()
        }
        logTagI(TAG, "stopRecording--over:${startRecordingFlag}")
    }

    fun savePreviewBitmap() {
        saveLastPreview = true
    }

    internal class CameraPreviewCallback : Camera.PreviewCallback {
        override fun onPreviewFrame(data: ByteArray?, camera: Camera) {
//            logTagD("song", "onPreviewFrame")
            if (data == null) {
                return
            }
//            frameCount++
//            val lng1 = System.currentTimeMillis() - lastSaveFrameTime
//            // 统计每1000ms有多少帧
//            if (lng1 > 1000) {
//                lastSaveFrameTime = System.currentTimeMillis()
//                logTagD("song", "1s平均帧率 $frameCount 时间-$lng1")
//                allFrameCount += frameCount
//                frameCount = 0
//            }
//            val lng = System.currentTimeMillis() - lastAllSaveFrameTime
//            // 每隔20s计算一下平均数据
//            if (lng > 20000) {
//                lastAllSaveFrameTime = System.currentTimeMillis()
//                val average = allFrameCount.toFloat() / 20f
//                logTagD("song", "20s平均帧率 $average  时间-$lng")
//                allFrameCount = 0
//            }

            onFirstFrameCallback?.let {
                // 不要影响预览流
                scope.launch {
                    logTagV(TAG, "获取到第一帧数据")
                    it()
                    onFirstFrameCallback = null
                }
            }
//            val copiedData = data.copyOf() // 这步很重要，它会复制一份新的数组，避免数据被修改

            //当启动录制的视频把视频源数据加入编码中
            MuxerManager.instance.addVideoFrameData(data)

            if (saveLastPreview) {
                frameCount++
            }

            var imageByte: ByteArray? = null
            if (takeAPicture || (saveLastPreview && frameCount > 10)) {
                val parameters = camera.parameters
                val width = parameters.previewSize.width
                val height = parameters.previewSize.height
                val yuvImage = YuvImage(data, parameters.previewFormat, width, height, null)
                val out = ByteArrayOutputStream()
                yuvImage.compressToJpeg(Rect(0, 0, width, height), 50, out)
                imageByte = out.toByteArray()
            }
            imageByte?.let {
                if (takeAPicture) {
                    // 将预览流转换为图片
                    takeAPicture = false
                    takeAPictureListener?.takeAPictureSuccessful(it)
                }

                if ((saveLastPreview && frameCount > 10)) {
                    saveLastPreview = false
                    scope.launch {
                        savePreviewFrame(it)
                    }
                }
            }

            camera.addCallbackBuffer(data)

        }
    }

    private suspend fun savePreviewFrame(data: ByteArray) {
        withContext(Dispatchers.Default) {
            val bitmap = BitmapFactory.decodeByteArray(data, 0, data.size)
            PreviewHelper.setPreviewBitmap(bitmap)
        }
    }

    interface CameraListener {
        // 相机状态监听
        fun onCameraErrorStatus(status: Int)
    }

    /**
     * 获取前置摄像头的id
     */
    private fun getFrontCameraId(): Int {
        val cameraCount = Camera.getNumberOfCameras()
        for (camIdx in 0 until cameraCount) {

            val cameraInfo = Camera.CameraInfo()

            Camera.getCameraInfo(camIdx, cameraInfo)
            logTagI(TAG, "id:${camIdx}, facing:${cameraInfo.facing}")
            if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
                return camIdx
            }
        }
        logTagW(TAG, "获取摄像头ID失败,cameraCount:${cameraCount}")
        return -1
    }

    // camera是否初始化
    fun isCameraInitialized() = mCamera != null


    /**
     * 拍照,比较截图预览流能够保证拍照的质量,但是很有限,大小大了1倍
     */
    fun takePictureWithApi(
        makeFileName: String,
        waterMarkIsChecked: Boolean,
        mainViewModel: MainViewModel,
        takePicResult: (Boolean) -> Unit
    ) {
        try {
            mCamera?.takePicture(null, null, null, Camera.PictureCallback { data, camera ->
                mainViewModel.launch(Dispatchers.IO) {
                    savePicture(makeFileName, data, waterMarkIsChecked)
                    mCamera?.startPreview()
                    takePicResult.invoke(true)
                }
            })
        } catch (e: Exception) {
            takePicResult.invoke(false)
        }


    }

    private suspend fun savePicture(
        fileName: String,
        data: ByteArray,
        waterMarkIsChecked: Boolean
    ) {
        // 生成图片文件
        val pictureFile =
            File(Environment.getExternalStorageDirectory(), Config.PHOTO_FILE_FOLDER_NAME)
        //pictureFile如果不存在就创建一个
        if (!pictureFile.exists()) {
            pictureFile.mkdirs()
        }
        //在pictureFile目录下创建一个文件
        val file = File(pictureFile, "$fileName.${Config.PHOTO_FILE_EXTENSION}")


        if (waterMarkIsChecked) {
            // 将原始字节数组转换为 Bitmap
            val originalBitmap =
                BitmapFactory.decodeByteArray(data, 0, data.size, BitmapFactory.Options().apply {
                    inMutable = true
                    outConfig = Bitmap.Config.ARGB_8888
                })
            val canvas = Canvas(originalBitmap)
            // 创建一个 Paint，用于绘制时间水印
            val paint = Paint()
            paint.color = Color.WHITE
            paint.textSize = 30f

            // 获取当前时间
            val timeStamp = getTimeStr(Config.PHOTO_FILE_WATERMARK_FORMAT)

            // 获取时间水印的长度
            val textWidth = paint.measureText(timeStamp)

            // 在新的 Bitmap 上绘制时间水印
            canvas.drawText(
                timeStamp,
                originalBitmap.width - textWidth - 16 /*right margin*/,
                originalBitmap.height - 20f /*bottom margin*/,
                paint
            )

            // 将Bitmap保存到文件
            originalBitmap.saveToFile(file)
        } else {
            // 把data数据写入到file文件中
            file.writeBytes(data)
        }
    }


    private fun unregisterCameraErrorCallBack() {
        mCamera?.setErrorCallback(null)
    }

    interface TakeAPictureListener {
        fun takeAPictureSuccessful(jpegArray: ByteArray)
    }
}