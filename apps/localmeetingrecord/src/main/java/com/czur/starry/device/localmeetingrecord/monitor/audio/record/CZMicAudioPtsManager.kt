package com.czur.starry.device.localmeetingrecord.monitor.audio.record

import android.os.SystemClock

/**
 * Created by 陈丰尧 on 2024/10/24
 */
class CZMicAudioPtsManager {
    private var startTime = -1L // 开始时间

    private var pauseStartTime = 0L // 暂停时间
    private var pauseDuration = 0L // 暂停总时长

    /**
     * 获取当前时间戳
     */
    fun getPts(): Long {
        if (startTime == -1L) {
            startTime = currentTimeMicroseconds()
            return 0L
        }
        return currentTimeMicroseconds() - startTime - pauseDuration
    }

    /**
     * 暂停
     */
    fun pause() {
        pauseStartTime = currentTimeMicroseconds()
    }

    /**
     * 恢复
     */
    fun resume() {
        pauseDuration += currentTimeMicroseconds() - pauseStartTime
        pauseStartTime = 0L
    }


    fun reset() {
        startTime = -1L
        pauseStartTime = 0L
        pauseDuration = 0L
    }

    private fun currentTimeMicroseconds(): Long = SystemClock.elapsedRealtimeNanos() / 1000L
}