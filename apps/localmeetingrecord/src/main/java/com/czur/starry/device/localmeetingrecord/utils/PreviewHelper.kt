package com.czur.starry.device.localmeetingrecord.utils

import android.graphics.Bitmap
import com.czur.starry.device.baselib.utils.blur
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/6/9
 * 用来保存预览帧模糊的工具类
 */
object PreviewHelper {
    private const val DEF_BLUR_RADIUS = 15
    private const val BLUR_TIMES = 3   // 模糊次数
    private const val SHRINK_RATIO = 8 // 缩小比例

    var burlPreviewBitmap: Bitmap? = null
        private set
    private val scope = MainScope()

    fun clear() {
        burlPreviewBitmap?.recycle()
        burlPreviewBitmap = null
    }

    fun setPreviewBitmap(bitmap: Bitmap) {
        scope.launch {
            val shrinkBitmap = shrinkBitmap(bitmap)
            val mirrorBitmap = mirrorBitmap(shrinkBitmap)
            burlPreviewBitmap = blurTimes(mirrorBitmap, BLUR_TIMES)

        }
    }

    /**
     * 缩小图片
     */
    private suspend fun shrinkBitmap(srcBitmap: Bitmap, scale: Int = SHRINK_RATIO): Bitmap {
        return withContext(Dispatchers.Default) {
            Bitmap.createScaledBitmap(
                srcBitmap,
                srcBitmap.width / scale,
                srcBitmap.height / scale,
                true
            ).also {
                srcBitmap.recycle()
            }
        }
    }

    /**
     * 镜像图片, 让预览帧画面与实际现实画面一致
     */
    private suspend fun mirrorBitmap(srcBitmap: Bitmap): Bitmap {
        return withContext(Dispatchers.Default) {
            val matrix = android.graphics.Matrix()
            matrix.setScale(-1f, 1f)
            Bitmap.createBitmap(srcBitmap, 0, 0, srcBitmap.width, srcBitmap.height, matrix, true)
        }.also {
            srcBitmap.recycle()
        }
    }

    /**
     * 多次模糊图片
     * 这样可以让图片更加模糊
     */
    private suspend fun blurTimes(srcBitmap: Bitmap, times: Int): Bitmap {
        var result = srcBitmap
        repeat(times) {
            result = result.blur(DEF_BLUR_RADIUS)
        }
        return result
    }
}