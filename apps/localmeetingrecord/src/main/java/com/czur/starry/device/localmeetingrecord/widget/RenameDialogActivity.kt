package com.czur.starry.device.localmeetingrecord.widget

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.view.Gravity
import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import androidx.core.view.isVisible
import androidx.core.widget.doOnTextChanged
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.addEmojiFilter
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.localmeetingrecord.Config.RENAME_DIALOG_SHOWED_ACTION
import com.czur.starry.device.localmeetingrecord.R
import com.czur.starry.device.localmeetingrecord.databinding.DialogRenameBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class RenameDialogActivity : CZViewBindingAty<DialogRenameBinding>() {
    companion object {
        const val TAG = "FloatingActivity"
    }

    private var savedFileDocumentPath: String = ""
    private var savedFilePath: String = ""
    private var savedFileName: String = ""
    private var savedFileExt: String = ""
    private var needToast: Boolean = true
    private var finishDelayTime: Long = 300L
    private var startTime = 0L

    override fun initWindow() {
        super.initWindow()
        startTime = SystemClock.elapsedRealtime()

        if (Constants.starryHWInfo.hasTouchScreen) {
            logTagV(TAG, "触控屏,添加偏移量")
            window.apply {
                val params = attributes
                params.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logTagI(
            TAG,
            "重命名窗口onNewIntent savedFileName${
                intent.getStringExtra("savedFileName").orEmpty()
            }"
        )
        handlePreIntent(intent)

        logTagI(TAG, "重命名窗口onNewIntent savedFileName$savedFileName")
        binding.inputDialogEt.setText(savedFileName)
        binding.inputDialogEt.setSelection(savedFileName.length)
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        logIntent(preIntent, TAG)

        savedFileDocumentPath = preIntent.getStringExtra("savedFileDocumentPath").orEmpty()
        savedFilePath = preIntent.getStringExtra("savedFilePath").orEmpty()
        savedFileName = preIntent.getStringExtra("savedFileName").orEmpty()
        savedFileExt = preIntent.getStringExtra("savedFileExt").orEmpty()
        needToast = preIntent.getBooleanExtra("needToast", true)
        finishDelayTime = preIntent.getLongExtra("finishDelayTime", 300L)
        logTagI(
            TAG,
            "savedFileDocumentPath$savedFileDocumentPath savedFilePath$savedFilePath savedFileName$savedFileName savedFileExt$savedFileExt needToast$needToast finishDelayTime$finishDelayTime"
        )
    }


    override fun DialogRenameBinding.initBindingViews() {
        logTagI(TAG, "打开重命名窗口")
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {   //对于Android 8.0及以上
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        } else {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }

        inputDialogEt.apply {
            setText(savedFileName)
            setSelection(savedFileName.length)

            doOnTextChanged { text, _, _, _ ->
                refreshConfirmEnable(text?.toString())
            }

            addEmojiFilter()
            // 字符长度为50, 中文2个字符
            showTextLength = 50

            setOnEditorActionListener { _, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                    || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
                ) {
                    if (confirmBtn.isEnabled && confirmBtn.isVisible) {
                        confirmBtn.performClick()
                    }
                }
                true
            }
        }

        confirmBtn.setOnClickListener {
            logTagI(TAG, "用户重命名")
            val savedFile = File(savedFilePath)
            val inputText = inputDialogEt.text.toString().trim()
            if (inputText.isNotEmpty()) {
                launch {
                    val newName = renameFile(savedFile, inputText)
                    if (needToast && newName.isNotEmpty()) {
                        showToast(newName)
                    } else {
                        logTagW(TAG, "重命名失败")
                    }
                    finish()
                }
            }
        }
        cancelBtn.setOnClickListener {
            logTagI(TAG, "用户取消重命名")
            val savedFile = File(savedFilePath)
            if (needToast) {
                showToast(savedFile.name)
            }

            finish()
        }

    }

    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            logTagI(TAG, "用户点击返回键")
            binding.cancelBtn.performClick()
            startTime = 0L
            return true
        }
        return super.onInterceptKeyDown(keyCode, event)
    }


    /**
     * 重命名
     */
    private suspend fun renameFile(srcFile: File, newName: String): String {
        return withContext(Dispatchers.IO) {
            val newFile = makeSureNewFileName(srcFile, newName)
            logTagI(TAG, "重命名:", srcFile.name, "->", newFile.name)

            srcFile.renameTo(newFile).yes {
                newFile.name
            }.otherwise {
                logTagW(TAG, "重命名失败")
                ""
            }
        }
    }

    private suspend fun makeSureNewFileName(srcFile: File, newName: String): File {
        var endText = ""
        var newFile = File(srcFile.parentFile, "${newName}.${srcFile.extension}")
        logTagI(TAG, "makeSureNewFileName ${newFile.path}")
        while (newFile.exists() && srcFile.path != newFile.path) {
            logTagI(TAG, "makeSureNewFileName -----${newFile.path}")
            endText += "(01)"
            newFile = File(srcFile.parentFile, "${newName}${endText}.${srcFile.extension}")
        }
        return newFile
    }

    private fun showToast(toastFileName: String) {
        toast(getString(R.string.toast_video_file_saved, toastFileName))
    }


    /**
     * 刷新确定按钮 是否可点击
     * 如果 输入框中没有输入内容,则确定按钮不可点击
     */
    private fun DialogRenameBinding.refreshConfirmEnable(inputText: String?) {
        confirmBtn.isEnabled = !inputText.isNullOrBlank()
    }

    override fun finish() {
        super.finish()
        val intent = Intent()
        intent.action = RENAME_DIALOG_SHOWED_ACTION
        sendBroadcast(intent)
        overridePendingTransition(0, 0);

        logTagI(TAG, "重命名窗口finish")
    }

    override fun onStop() {
        super.onStop()
        if (SystemClock.elapsedRealtime() - startTime < 1000) {
            logTagW(TAG, "重命名窗口onStop过快，可能是误触发，重新启动")
            startActivity(intent)
            return
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        logTagI(TAG, "重命名窗口onDestroy")

    }
}