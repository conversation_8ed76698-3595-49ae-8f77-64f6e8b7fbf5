package com.czur.starry.device.localmeetingrecord.widget

import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import androidx.core.animation.doOnEnd
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.localmeetingrecord.Config.DEFAULT_RECORD_MODE
import com.czur.starry.device.localmeetingrecord.RecordModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlin.math.abs


/**
 * Created by <PERSON><PERSON><PERSON> on 1/12/21
 * 制作一个view,可以随手势拖动来左右滑动,手指离开时,会自动滑动到最近的一个tab
 * 也可以点击tab来切换,点击tab时,会自动滑动到对应的tab
 */
class SlideSelectTabView(context: Context, attrs: AttributeSet?) : View(context, attrs) {

    constructor(context: Context) : this(context, null)

    companion object {
        private const val unSelectedColor = 0xFFFFFFFF.toInt()
        private const val selectedColor = 0xFFFFC107.toInt()
        private val PADDING_LEFT = 20
        private val PADDING_RIGHT = 20
        private val PADDING_TOP = 20
        private val PADDING_BOTTOM = 20

    }


    private val textPadding = PADDING_LEFT + PADDING_RIGHT

    var selectResultFlow = MutableSharedFlow<RecordModel>()

    //两个颜色的差值
    private val colorInterpolator = (selectedColor - unSelectedColor).toFloat()
    private val argbEvaluator = ArgbEvaluator()

    // 每次要开启动画前的回调
    var beforeAnimListener: (() -> Unit)? = null

    var defaultSelectTabIndex = DEFAULT_RECORD_MODE.number
        set(value) {
            field = value
            if (value == -1) {
                return
            }
            selectTabIndex = value
        }

    private var selectTabIndex = -1
        set(value) {
            if (value < 0 || value >= tabList.size || value == oldSelectTabIndex) {
                return
            }
            if (selectTabIndex == -1) {
                oldSelectTabIndex = value
            }

            field = value
            if (startX == -1f) {//初始化,不需要动画
                startX = getStartX(value)
            } else {
                startAnim()
            }
            oldSelectTabIndex = selectTabIndex
        }

    private var oldSelectTabIndex = -1

    private var paint = Paint().apply {
        color = Color.YELLOW
        textSize = 30f
        setPadding(20, 20, 20, 20)
        textAlign = Paint.Align.LEFT
        typeface = Typeface.DEFAULT_BOLD
        setShadowLayer(10f, 0f, 0f, Color.BLACK)
    }

    var tabList = mutableListOf<RecordModel>()

    private var startX = -1f
        set(value) {
            field = value
            invalidate()
        }

    private var targetStartX = 0f

    private var isMoved = false
    private var downX = 0f
    private var downY = 0f

    var touchable = MutableStateFlow<Boolean>(true)// 是否可以进行触摸事件, 动画运行的时候不可以

    fun getTargetMode(): RecordModel {
        if (selectTabIndex == -1) {
            return RecordModel.values()[defaultSelectTabIndex]
        }
        return RecordModel.values()[selectTabIndex]
    }

    //创建一个移动100距离的属性动画,耗时300ms
    private fun startAnim() {
        beforeAnimListener?.invoke()

        CoroutineScope(Job()).launch {
            touchable.emit(false)
        }
        val oldStartX = getStartX(oldSelectTabIndex)
        targetStartX = getStartX(selectTabIndex)
        val animator = ObjectAnimator.ofFloat(this, "startX", oldStartX, targetStartX)
        animator.duration = 350
        animator.interpolator = DecelerateInterpolator()
        animator.doOnEnd {
            CoroutineScope(Job()).launch {
                selectResultFlow.emit(RecordModel.values()[selectTabIndex])
                delay(100)
                touchable.emit(true)
            }

        }
        animator.start()
    }

    /**
     * 计算屏幕中心点的x坐标
     */
    private fun getCenterX(): Int {
        return width / 2
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        var drawStartX = startX
        if (tabList.size > 0) {
            //计算每个text的中心点到屏幕中心点的距离
            val pair = getNearTwoIndex(getDistanceList())
            val firstIndex = pair.first.first
            var secondIndex = pair.first.second
            val firstDistance = pair.second.first
            val secondDistance = pair.second.second
            if (firstDistance == 0f) {
                secondIndex = -1
            }
            // 横向画出四个text
            for (i in 0 until tabList.size) {
                when (i) {
                    firstIndex -> {
                        val colorByDistance =
                            getColorByDistance(firstDistance, tabList[i].recordModeName)
                        paint.color = colorByDistance
                    }

                    secondIndex -> {
                        val colorByDistance =
                            getColorByDistance(secondDistance, tabList[i].recordModeName)
                        paint.color = colorByDistance
                    }

                    else -> {
                        paint.color = unSelectedColor
                    }
                }


                val text = tabList[i].recordModeName
                //测量字体高度
                val fontMetrics = paint.fontMetrics

                val y = height.toFloat() / 2 + abs(fontMetrics.ascent / 2)
                canvas?.drawText(text, drawStartX + PADDING_LEFT, y, paint)
                drawStartX += measureTextWidth(text)

            }
        }


    }

    private fun getColorByDistance(distance: Float, textStr: String): Int {
        var percent = (distance / measureTextWidth(textStr)).coerceIn(0f, 1f)
        return argbEvaluator.evaluate(percent, selectedColor, unSelectedColor) as Int
    }

    /**
     * 找到list中,相对的值最小的两个
     */
    private fun getNearTwoIndex(list: MutableList<Float>): Pair<Pair<Int, Int>, Pair<Float, Float>> {
        val newList = mutableListOf<Float>()
        newList.addAll(list)
        val firstDistance = newList.minOrNull()!!
        val firstIndex = list.indexOf(firstDistance)
        newList.remove(firstDistance)
        val secondDistance = newList.minOrNull()!!
        val secondIndex = list.indexOf(secondDistance)

        return Pair(Pair(firstIndex, secondIndex), Pair(firstDistance, secondDistance))
    }


    /**
     * 计算每个text的中心点到屏幕中心点的距离,得到一个距离的list
     */
    private fun getDistanceList(): MutableList<Float> {
        val centerX = getCenterX()
        val distanceList = mutableListOf<Float>()
        var startXLocal = startX
        for (i in 0 until tabList.size) {
            val measureText = measureTextWidth(tabList[i].recordModeName)
            val distance = centerX - startXLocal - measureText / 2 //中心点到每个text的中心点的距离
            startXLocal += measureText
            distanceList.add(abs(distance))
        }
        return distanceList
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
    }

    /**
     * 重写onTouchEvent方法,处理点击手势,判断是否点击了drawText
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!touchable.value) {
            return super.onTouchEvent(event)
        }
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                downX = event.x
                downY = event.y
                isMoved = false
            }

            MotionEvent.ACTION_MOVE -> {
                //移动超过10个像素,就认为是移动了
                if (abs(event.x - downX) > 10 || abs(event.y - downY) > 10) {
                    isMoved = true
                    return true
                }
            }

            MotionEvent.ACTION_UP -> {
                if (isMoved) {
                    return true
                }

                for (i in 0 until getRectFList().size) {
                    val rectF = getRectFList()[i]
                    if (rectF.contains(downX, downY)) {
                        selectTabIndex = i
                        return true
                    }
                }
            }
        }
        return true
    }

    /**
     * 根据中心选中的text坐标,计算其他text的坐标
     */
    private fun getRectFList(): MutableList<RectF> {
        val rectList = mutableListOf<RectF>()
        var startX = getStartX(selectTabIndex) // 整体的初始坐标
        val centerY = height / 2
        for (i in 0 until tabList.size) {
            val textWidth = measureTextWidth(tabList[i].recordModeName)
            val textHeight = abs(paint.descent()) + abs(paint.ascent())
            val rectF = RectF(
                startX, centerY - textHeight / 2,
                startX + textWidth, centerY + textHeight / 2
            )
            rectList.add(rectF)
            startX += textWidth
        }
        return rectList
    }

    private fun getStartX(selectTabIndex: Int): Float {
        val centerX = getCenterX()
        //计算选中的view的字体宽度
        val measureText = measureTextWidth(tabList[selectTabIndex].recordModeName)
        val centerViewStartX = centerX - measureText / 2
        var startX = centerViewStartX //初始点坐标
        if (selectTabIndex == 0) {
            startX = centerViewStartX
        } else {
            for (i in 0 until selectTabIndex) {
                startX -= measureTextWidth(tabList[i].recordModeName)
            }
        }
        return startX
    }


    private fun measureTextWidth(text: String): Float {
        return paint.measureText(text) + textPadding
    }

}