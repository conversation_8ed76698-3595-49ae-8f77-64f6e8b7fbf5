<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bgcl"
    android:layout_width="416px"
    android:layout_height="105px"
    android:background="@drawable/blue_50_58px_bg"
    android:padding="8px"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/blue2_50_58px_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/redPointView"
        android:layout_width="20px"
        android:layout_height="20px"
        android:layout_marginStart="30px"
        app:breathEffect="true"
        app:circleColor="@color/notice_read"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/record_time_tv"
        android:layout_width="120px"
        android:layout_height="wrap_content"
        android:layout_marginStart="30px"
        android:gravity="center"
        android:text="17:12:12"
        android:textColor="@color/white"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/redPointView"
        app:layout_constraintTop_toBottomOf="@+id/record_state_tv" />

    <TextView
        android:id="@+id/record_state_tv"
        android:layout_width="160px"
        android:layout_height="wrap_content"
        android:layout_marginStart="30px"
        android:gravity="left"
        android:text="@string/str_is_recording_audio"
        android:textColor="@color/white"
        android:textSize="18px"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintBottom_toTopOf="@+id/record_time_tv"
        app:layout_constraintStart_toEndOf="@+id/redPointView"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/full_screen_iv"
        android:layout_width="53px"
        android:layout_height="53px"
        android:layout_marginEnd="20px"
        android:src="@drawable/ic_alert_window_full_screen"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/control_iv"
        android:layout_width="53px"
        android:layout_height="53px"
        android:layout_marginEnd="28px"
        android:src="@drawable/ic_play_white"
        app:layout_constraintBottom_toBottomOf="@+id/full_screen_iv"
        app:layout_constraintEnd_toStartOf="@+id/full_screen_iv"
        app:layout_constraintTop_toTopOf="@+id/full_screen_iv" />

    <com.czur.starry.device.localmeetingrecord.widget.RecordView
        android:id="@+id/stop_iv"
        android:layout_width="53px"
        android:layout_height="53px"
        android:layout_marginEnd="18px"
        android:src="@drawable/ic_stop_record"
        app:layout_constraintBottom_toBottomOf="@+id/control_iv"
        app:layout_constraintEnd_toStartOf="@+id/control_iv"
        app:layout_constraintTop_toTopOf="@+id/control_iv" />
</androidx.constraintlayout.widget.ConstraintLayout>