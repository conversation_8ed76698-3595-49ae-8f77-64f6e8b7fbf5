<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded">


    <androidx.constraintlayout.widget.Group
        android:id="@+id/record_btns_group"
        android:layout_width="0px"
        android:layout_height="0px"
        android:visibility="visible"
        app:constraint_referenced_ids="dividerBottom,actionBg,stop_iv,pause_iv,record_time_tv" />

    <FrameLayout
        android:id="@+id/bg_video_fl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/blue_50_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/dividerBottom"/>
    <View
        android:id="@+id/actionBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="8px"
        android:background="#80000000"
        app:layout_constraintBottom_toBottomOf="@id/bg_video_fl"
        app:layout_constraintEnd_toEndOf="@id/bg_video_fl"
        app:layout_constraintStart_toStartOf="@id/bg_video_fl"
        app:layout_constraintTop_toTopOf="@id/bg_video_fl" />

    <com.czur.starry.device.localmeetingrecord.widget.CameraView
        android:id="@+id/surfaceViewadc"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="8px"
        app:cameraDisplayMode="texture"
        app:layout_constraintBottom_toTopOf="@+id/dividerBottom"
        app:layout_constraintEnd_toEndOf="@+id/bg_video_fl"
        app:layout_constraintStart_toStartOf="@+id/bg_video_fl"
        app:layout_constraintTop_toTopOf="@+id/bg_video_fl" />

    <FrameLayout
        android:id="@+id/dividerBottom"
        android:layout_width="match_parent"
        android:layout_height="56px"
        android:background="#B3000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/full_screen_iv"
        android:layout_width="53px"
        android:layout_height="53px"
        android:layout_marginTop="10px"
        android:layout_marginEnd="10px"
        android:src="@drawable/ic_alert_window_full_screen"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/actionBg"
        app:layout_constraintTop_toTopOf="@id/actionBg" />

    <ImageView
        android:id="@+id/pause_iv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginStart="16px"
        android:src="@drawable/ic_pause_white"
        app:layout_constraintTop_toTopOf="@+id/dividerBottom"
        app:layout_constraintBottom_toBottomOf="@+id/dividerBottom"
        app:layout_constraintStart_toEndOf="@id/stop_iv" />

    <ImageView
        android:id="@+id/stop_iv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginStart="16px"
        android:src="@drawable/ic_stop_record"
        app:layout_goneMarginStart="16px"
        app:layout_constraintTop_toTopOf="@+id/dividerBottom"
        app:layout_constraintBottom_toBottomOf="@+id/dividerBottom"
        app:layout_constraintStart_toStartOf="@+id/dividerBottom" />


    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/redpoint"
        android:layout_width="20px"
        android:layout_height="20px"
        android:layout_marginStart="10px"
        android:layout_marginTop="10px"
        android:visibility="gone"
        app:breathEffect="true"
        app:circleColor="@color/notice_read"
        app:layout_constraintStart_toStartOf="@id/surfaceViewadc"
        app:layout_constraintTop_toTopOf="@id/surfaceViewadc" />

    <TextView
        android:id="@+id/record_time_tv"
        android:layout_width="120px"
        android:layout_height="wrap_content"
        android:layout_marginStart="10px"
        android:layout_marginEnd="16px"
        android:gravity="center"
        android:text="00:00:00"
        android:textColor="@color/white"
        android:textSize="28px"
        app:layout_constraintTop_toTopOf="@+id/dividerBottom"
        app:layout_constraintBottom_toBottomOf="@+id/dividerBottom"
        app:layout_constraintEnd_toEndOf="@+id/dividerBottom" />


</androidx.constraintlayout.widget.ConstraintLayout>