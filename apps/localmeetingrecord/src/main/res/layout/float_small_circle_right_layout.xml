<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bgcl"
    android:layout_width="63px"
    android:layout_height="105px"
    android:background="@drawable/blue_50_58px_half_c_r_bg"
    android:elevation="10px"
    android:paddingVertical="8px"
    android:paddingLeft="8px"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/blue2_50_58px_half_c_r_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/redPointView"
        android:layout_width="20px"
        android:layout_height="20px"
        app:breathEffect="true"
        app:circleColor="@color/notice_read"
        android:layout_marginRight="5px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/constraintLayout"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>