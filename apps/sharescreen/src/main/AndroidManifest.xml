<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="Deprecated,PermissionImpliesUnsupportedChromeOsHardware,ProtectedPermissions">

    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MANAGE_USB" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/BaseAppTheme">

        <activity
            android:name=".MainActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/MainAty" />

        <activity
            android:name=".BootActivity"
            android:exported="true"
            android:theme="@style/BootAty">
            <intent-filter>
                <action android:name="com.czur.starry.device.sharescreen.BOOT_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name=".DeviceNameAlertWindowService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.sharescreen.DEVICE_NAME_ALERT_WINDOW_SERVICE" />
            </intent-filter>
        </service>

        <activity
            android:name=".peripheral.PeripheralModeGuideActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher_peripheral"
            android:launchMode="singleInstance"
            android:theme="@style/ByomGuideActivityThemeUSB">
            <intent-filter>
                <action android:name="com.czur.starry.device.sharescreen.PERIPHERAL_MODE_GUIDE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".peripheral.PeripheralModeGuideEShareActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher_peripheral_eshare"
            android:launchMode="singleInstance"
            android:theme="@style/ByomGuideActivityTheme">
            <intent-filter>
                <action android:name="com.czur.starry.device.sharescreen.PERIPHERAL_MODE_ESHARE_GUIDE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

    </application>

</manifest>