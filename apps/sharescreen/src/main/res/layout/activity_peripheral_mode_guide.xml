<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title=""
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/peripheralModeTitle"
        style="@style/byom_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/str_byom_USB_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/peripheralModeSub"
        style="@style/byom_content_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5px"
        android:text="@string/str_byom_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/peripheralModeTitle" />


    <com.noober.background.view.BLView
        android:id="@+id/switchBGView"
        android:layout_width="416px"
        android:layout_height="64px"
        android:layout_marginTop="144px"
        app:bl_corners_radius="32px"
        app:bl_solid_color="#33000000"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/usbModeTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_byom_USB_title"
        android:textColor="@color/white"
        android:textSize="22px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/switchBGView"
        app:layout_constraintLeft_toLeftOf="@id/switchBGView"
        app:layout_constraintTop_toTopOf="@id/switchBGView" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/usbModeSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginLeft="15px"
        app:czSwitchBgOffColor="#BB448F"
        app:czSwitchBgOnColor="@color/white"
        app:czSwitchBorderColor="@color/white"
        app:czSwitchTextOffColor="@color/white"
        app:czSwitchTextOnColor="#BB448F"
        app:czSwitchThumbOffColor="@color/white"
        app:czSwitchThumbOnColor="#BB448F"
        app:layout_constraintBottom_toBottomOf="@id/usbModeTitleTv"
        app:layout_constraintLeft_toRightOf="@id/usbModeTitleTv"
        app:layout_constraintTop_toTopOf="@id/usbModeTitleTv" />

    <ImageView
        android:id="@+id/usbModeIv"
        android:layout_width="490px"
        android:layout_height="452px"
        android:layout_marginLeft="298px"
        android:layout_marginTop="364px"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="95px"
        android:orientation="vertical"
        app:constraint_referenced_ids="usbStep1,usbStep2,usbStep3,usbStep4"
        app:flow_horizontalAlign="start"
        app:flow_verticalGap="30px"
        app:layout_constraintBottom_toBottomOf="@id/usbModeIv"
        app:layout_constraintLeft_toRightOf="@id/usbModeIv"
        app:layout_constraintTop_toTopOf="@id/usbModeIv" />




    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep1"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        app:stepIcon="@drawable/icon_number1"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content1"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep2"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        app:stepIcon="@drawable/icon_number2"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content2"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep3"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        app:stepIcon="@drawable/icon_number3"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content3"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep4"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        app:stepIcon="@drawable/icon_number4"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content4"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />
</androidx.constraintlayout.widget.ConstraintLayout>