package com.czur.starry.device.smallroundscreen.animation

import android.content.Context
import android.widget.ImageView
import android.widget.VideoView
import com.czur.starry.device.smallroundscreen.R
import com.czur.starry.device.smallroundscreen.widget.TextureVideoView

class HdmiNormalAnimationDrawable(
    context: Context,
    videoView: TextureVideoView
) : BaseVideoAnimation(context, videoView, "android.resource://" + context.packageName + "/" + R.raw.hdmi_out_normal_video, true)

class HdmiFlashingAnimationDrawable(
    context: Context,
    imageView: ImageView
) : BaseHdmiAnimationDrawable(context, imageView, "hdmi_flashing")

class HdmiOutNormalAnimationDrawable(
    context: Context,
    imageView: ImageView
) : BaseHdmiAnimationDrawable(context, imageView, "hdmi_out_normal")

class HdmiOutUnconnectedAnimationDrawable(
    context: Context,
    imageView: ImageView
) : BaseHdmiAnimationDrawable(context, imageView, "hdmi_out_unconnected")

class HdmiByomBgAnimationDrawable(
    context: Context,
    videoView: TextureVideoView
) : BaseVideoAnimation(context, videoView, "android.resource://" + context.packageName + "/" + R.raw.byom_peripheral_video, true, true)

class HdmiByomWordAppearAnimationDrawable(
    context: Context,
    imageView: ImageView
) : BaseHdmiAnimationDrawable(context, imageView, "byom_hdmi_word_appear")

class HdmiByomWordDisappearAnimationDrawable(
    context: Context,
    imageView: ImageView,
    onAnimationEnd: (() -> Unit)? = null
) : BaseHdmiAnimationDrawable(context, imageView, "byom_hdmi_word_disappear", onAnimationEnd = onAnimationEnd)

class UsbByomBgAnimationDrawable(
    context: Context,
    videoView: TextureVideoView
) : BaseVideoAnimation(context, videoView, "android.resource://" + context.packageName + "/" + R.raw.usb_peripheral_video, true, true)

class UsbByomWordAppearAnimationDrawable(
    context: Context,
    imageView: ImageView
) : BaseHdmiAnimationDrawable(context, imageView, "byom_usb_word_appear")

class UsbByomWordDisappearAnimationDrawable(
    context: Context,
    imageView: ImageView,
    onAnimationEnd: (() -> Unit)? = null
) : BaseHdmiAnimationDrawable(context, imageView, "byom_usb_word_disappear", onAnimationEnd = onAnimationEnd)

class HdmiInExtractAnimationDrawable(
    context: Context,
    imageView: ImageView,
    onAnimationEnd: (() -> Unit)? = null
) : BaseHdmiAnimationDrawable(context, imageView, "hdmi_in_extract", fadeEffect = true, onAnimationEnd = onAnimationEnd)

class HdmiInInsertAnimationDrawable(
    context: Context,
    imageView: ImageView,
    onAnimationEnd: (() -> Unit)? = null
) : BaseHdmiAnimationDrawable(context, imageView, "hdmi_in_insert", fadeEffect = true, onAnimationEnd = onAnimationEnd)

class UsbExtractAnimationDrawable(
    context: Context,
    imageView: ImageView,
    onAnimationEnd: (() -> Unit)? = null
) : BaseHdmiAnimationDrawable(context, imageView, "usb_extract", fadeEffect = true, onAnimationEnd = onAnimationEnd)

class UsbInsertAnimationDrawable(
    context: Context,
    imageView: ImageView,
    onAnimationEnd: (() -> Unit)? = null
) : BaseHdmiAnimationDrawable(context, imageView, "usb_insert", fadeEffect = true, onAnimationEnd = onAnimationEnd)