package com.czur.starry.device.smallroundscreen.animation

class AnimationPlayerImpl : CustomAnimationPlayer {
    private var currentAnimation: CustomAnimation? = null

    override fun play(animation: CustomAnimation) {
        currentAnimation?.endAnimation()
        currentAnimation = animation
        currentAnimation?.startAnimation()
    }

    override fun pause() {
        currentAnimation?.stopAnimation()
    }

    override fun stop() {
        currentAnimation?.endAnimation()
        currentAnimation = null
    }
}
