package com.czur.starry.device.smallroundscreen.share

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_AUDIO_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_BYOM_CAMERA_RUNNING
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_DEVICE_NAME
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.eshare.serverlibrary.api.EShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

class ShareViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "ShareViewModel"
    }
    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(application)
    }

    val showShareNameLive = DifferentLiveData("")
    private var showShareName by LiveDataDelegate(showShareNameLive)

    // 宜享BYOM的状态
    private val _peripheralByomRunningFlow = MutableStateFlow(false)
    val peripheralByomRunningFlow = _peripheralByomRunningFlow.asStateFlow()

    // USB外设模式的状态
    private val _peripheralUSBRunningFlow = MutableStateFlow(false)
    val peripheralUSBRunningFlow = _peripheralUSBRunningFlow.asStateFlow()

    private val eShareCallback: EShareCallback = object : SimpleEShareCallback() {
        override fun onSettingsChanged(key: String, newValue: Any?) {
            logTagD(TAG, "eShareCallback key = ${key}")
            when (key) {
                E_SHARE_DEVICE_NAME -> {
                    showShareName = newValue?.toString() ?: ""
                }

                E_SHARE_BYOM_CAMERA_RUNNING, E_SHARE_BYOM_AUDIO_RUNNING -> {
                    _peripheralByomRunningFlow.value = eShareServerSDK.isBYOMRunning
                    logTagD(TAG, "eShareCallback isByomrunning = ${eShareServerSDK.isBYOMRunning}")
                }

                else -> {}
            }
        }
    }

    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(application)
    }

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy { category, eventID, para1, extend1, extend2 ->
            logTagD(
                TAG,
                "category = $category, eventID = $eventID, para1 = $para1, extend1 = $extend1, extend2 = $extend2"
            )
            if (category == 3) {
                launch {
                    refreshGadgetState()
                }
            }
        }
    }

    private val noticeListener: (msg: NoticeMsg) -> Unit = {
        logTagD(TAG, "gadgetState改变, 刷新USB外设模式")
        launch {
            refreshGadgetState()
        }
    }

    init {
        launch {
            eShareServerSDK.registerCallback(eShareCallback)
            loadShareInfo()
        }

        launch {
            refreshGadgetState()   // 刷新USB外设模式
            refreshBYOMState() // 刷新宜享BYOM状态
        }

        launch {
            peripheralUSBRunningFlow.collect {
                logTagD(TAG, "peripheralUSBRunningFlow = $it")
                if (it && _peripheralByomRunningFlow.value) {
                    _peripheralByomRunningFlow.value = false
                }
            }
        }

        netStatusUtil.startWatching()
        registerNotice()
    }

    private fun registerNotice() {
        NoticeHandler.register(
            MsgType(MsgType.SYNC, MsgType.COMMON_PERIPHERAL_USB_CHANGE),
            listener = noticeListener
        )
    }

    /**
     * 刷新无线投屏信息
     */
    suspend fun loadShareInfo() = withContext(Dispatchers.IO) {
        refreshShareName()
    }

    /**
     * 刷新USB外设模式
     */
    suspend fun refreshGadgetState() = withContext(Dispatchers.IO) {
        logTagV(TAG, "refreshGadgetState")
        _peripheralUSBRunningFlow.value =
            systemManager.getGadgetMode() == SystemManagerProxy.USBModeState.USB_GADGET_STREAM_ON
    }

    suspend fun refreshBYOMState() = withContext(Dispatchers.IO) {
        logTagD(TAG, "refreshBYOMState")
        _peripheralByomRunningFlow.value = eShareServerSDK.isBYOMRunning
    }

    /**
     * 刷新设备名称
     */
    private suspend fun refreshShareName() = withContext(Dispatchers.IO) {
        showShareName = eShareServerSDK.deviceName
    }

    override fun onCleared() {
        super.onCleared()
        logTagD(TAG, "Launcher-ShareViewMode onCleared")

        NoticeHandler.unRegister(
            MsgType(MsgType.SYNC, MsgType.COMMON_PERIPHERAL_USB_CHANGE),
            noticeListener
        )
        netStatusUtil.stopWatching()
        eShareServerSDK.unregisterCallback(eShareCallback)
    }
}